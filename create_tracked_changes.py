#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a tracked changes version of the LaTeX manuscript
showing all modifications made in response to reviewer feedback.
"""

import re
import os

def create_tracked_changes_document():
    """Create a tracked changes version of the manuscript."""
    
    # Read the original template
    with open('template.tex', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define the changes to be marked
    changes = [
        # Title changes
        ('Harmful Algae Blooms', 'Harmful Algal Blooms'),
        
        # Abstract changes
        ('machine learning', 'ML'),
        ('We utilized twelve features', 'We collected 19 environmental features from satellite and climate data sources, from which we selected 12 key predictive features through feature importance analysis'),
        ('LST_Night', 'LST_Day'),
        
        # Acronym consistency changes throughout document
        ('Machine Learning', 'ML'),
        ('Total Phosphorous', 'TP'),
        ('Total Suspended Solids', 'TSS'),
        ('Electric Conductivity', 'EC'),
        ('Land Surface Temperature', 'LST'),
        ('Carbon Monoxide', 'CO'),
        ('Southern Illinois University', 'SIU'),
        ('SHapley Additive exPlanations', 'SHAP'),
        
        # Methodology changes
        ('Nested cross-validation was implemented with 10-fold outer cross-validation and 5-fold inner cross-validation', 'Cross-validation was performed using 5-fold validation'),
        ('Temporal validation was performed by training models on data from 2020-2021 and testing on 2022 data', 'Model validation focused on spatial cross-validation within the study period'),
    ]
    
    # Create tracked changes version with color highlighting
    tracked_content = content.replace(
        '\\usepackage{subfig}',
        '''\\usepackage{subfig}
\\usepackage{xcolor}
\\usepackage{soul}

% Define colors for tracked changes
\\definecolor{addedtext}{RGB}{0,100,0}
\\definecolor{deletedtext}{RGB}{200,0,0}
\\definecolor{changedtext}{RGB}{0,0,200}

% Define commands for tracked changes
\\newcommand{\\added}[1]{\\textcolor{addedtext}{\\textbf{[ADDED: #1]}}}
\\newcommand{\\deleted}[1]{\\textcolor{deletedtext}{\\sout{#1}}}
\\newcommand{\\changed}[2]{\\textcolor{deletedtext}{\\sout{#1}} \\textcolor{changedtext}{\\textbf{#2}}}'''
    )
    
    # Apply specific changes with tracking markup
    for old_text, new_text in changes:
        if old_text in tracked_content:
            tracked_content = tracked_content.replace(
                old_text, 
                f'\\changed{{{old_text}}}{{{new_text}}}'
            )
    
    # Add specific additions
    additions = [
        ('This study presents a comprehensive', 'This study presents a comprehensive \\added{machine learning (ML)}'),
        ('analysis revealed that', '\\added{SHapley Additive exPlanations (SHAP)} analysis revealed that'),
        ('The economic impact', '\\added{The economic impact of HABs is substantial, with freshwater HABs alone causing approximately \\$62 million in annual damages in the United States, while total HAB-related costs reach \\$82 million annually when including both marine and freshwater systems \\cite{lopez2008economic}.}'),
    ]
    
    for marker, addition in additions:
        tracked_content = tracked_content.replace(marker, addition)
    
    # Add deletions
    deletions = [
        'Thus, traditional laboratory methodology cannot capture spatiotemporal variations in HAB across the water body.',
    ]
    
    for deletion in deletions:
        if deletion in tracked_content:
            tracked_content = tracked_content.replace(
                deletion, 
                f'\\deleted{{{deletion}}}'
            )
    
    # Write the tracked changes version
    with open('template_tracked_changes_final.tex', 'w', encoding='utf-8') as f:
        f.write(tracked_content)
    
    print("Tracked changes document created: template_tracked_changes_final.tex")

if __name__ == "__main__":
    create_tracked_changes_document()
