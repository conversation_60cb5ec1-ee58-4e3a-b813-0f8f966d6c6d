#!/usr/bin/env python3
"""
Quality Assurance Check for Extracted Tables and Figures
Verifies that all extracted content matches the original manuscript exactly.
"""

import os
import re
from pathlib import Path
from docx import Document
from PIL import Image
import pandas as pd

class QualityAssuranceChecker:
    def __init__(self, tex_file_path, extracted_dir):
        self.tex_file_path = tex_file_path
        self.extracted_dir = Path(extracted_dir)
        
        # Read the LaTeX file
        with open(tex_file_path, 'r', encoding='utf-8') as f:
            self.tex_content = f.read()
    
    def check_table_references(self):
        """Check that all table references in the manuscript have corresponding extracted files."""
        print("Checking table references...")
        
        # Find all table references in the text
        table_refs = re.findall(r'Table\s+\\ref\{([^}]+)\}', self.tex_content)
        table_refs += re.findall(r'Table\s+(\d+)', self.tex_content)
        
        # Find all table labels
        table_labels = re.findall(r'\\label\{([^}]*tab[^}]*)\}', self.tex_content, re.IGNORECASE)
        
        print(f"Found table references: {set(table_refs)}")
        print(f"Found table labels: {set(table_labels)}")
        
        # Check extracted table files
        table_files = sorted([f for f in os.listdir(self.extracted_dir) if f.startswith('Table') and f.endswith('.docx')])
        print(f"Extracted table files: {table_files}")
        
        # Verify numbering consistency
        expected_tables = []
        for i in range(1, len(table_files) + 1):
            expected_tables.append(f"Table {i}.docx")
        
        missing_tables = set(expected_tables) - set(table_files)
        extra_tables = set(table_files) - set(expected_tables)
        
        if missing_tables:
            print(f"❌ Missing table files: {missing_tables}")
        if extra_tables:
            print(f"⚠️  Extra table files: {extra_tables}")
        if not missing_tables and not extra_tables:
            print("✅ All table files present and correctly numbered")
        
        return len(table_files), missing_tables, extra_tables
    
    def check_figure_references(self):
        """Check that all figure references in the manuscript have corresponding extracted files."""
        print("\nChecking figure references...")
        
        # Find all figure references in the text
        figure_refs = re.findall(r'Figure\s+\\ref\{([^}]+)\}', self.tex_content)
        figure_refs += re.findall(r'Figure\s+(\d+)', self.tex_content)
        
        # Find all figure labels
        figure_labels = re.findall(r'\\label\{([^}]*fig[^}]*)\}', self.tex_content, re.IGNORECASE)
        
        print(f"Found figure references: {set(figure_refs)}")
        print(f"Found figure labels: {set(figure_labels)}")
        
        # Check extracted figure files
        figure_files = sorted([f for f in os.listdir(self.extracted_dir) 
                              if f.startswith('Figure') and not f.endswith('.docx')])
        print(f"Extracted figure files: {figure_files}")
        
        # Group by figure number
        figure_groups = {}
        for fig_file in figure_files:
            # Extract figure number
            match = re.match(r'Figure\s+(\d+)', fig_file)
            if match:
                fig_num = int(match.group(1))
                if fig_num not in figure_groups:
                    figure_groups[fig_num] = []
                figure_groups[fig_num].append(fig_file)
        
        print(f"Figure groups: {figure_groups}")
        
        # Check for missing figures
        max_fig_num = max(figure_groups.keys()) if figure_groups else 0
        missing_figures = []
        for i in range(1, max_fig_num + 1):
            if i not in figure_groups:
                missing_figures.append(f"Figure {i}")
        
        if missing_figures:
            print(f"❌ Missing figure numbers: {missing_figures}")
        else:
            print("✅ All figure numbers present")
        
        return len(figure_groups), missing_figures
    
    def check_file_quality(self):
        """Check the quality and properties of extracted files."""
        print("\nChecking file quality...")
        
        # Check table files
        table_files = [f for f in os.listdir(self.extracted_dir) if f.startswith('Table') and f.endswith('.docx')]
        for table_file in sorted(table_files):
            file_path = self.extracted_dir / table_file
            try:
                doc = Document(file_path)
                table_count = len(doc.tables)
                paragraph_count = len(doc.paragraphs)
                print(f"✅ {table_file}: {table_count} tables, {paragraph_count} paragraphs")
            except Exception as e:
                print(f"❌ {table_file}: Error reading file - {e}")
        
        # Check figure files
        figure_files = [f for f in os.listdir(self.extracted_dir) 
                       if f.startswith('Figure') and not f.endswith('.docx')]
        for figure_file in sorted(figure_files):
            file_path = self.extracted_dir / figure_file
            try:
                if figure_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    with Image.open(file_path) as img:
                        width, height = img.size
                        mode = img.mode
                        print(f"✅ {figure_file}: {width}x{height} pixels, {mode} mode")
                elif figure_file.lower().endswith('.pdf'):
                    file_size = os.path.getsize(file_path)
                    print(f"✅ {figure_file}: PDF, {file_size} bytes")
                else:
                    file_size = os.path.getsize(file_path)
                    print(f"✅ {figure_file}: {file_size} bytes")
            except Exception as e:
                print(f"❌ {figure_file}: Error reading file - {e}")
    
    def check_manuscript_completeness(self):
        """Check that all tables and figures mentioned in the manuscript are extracted."""
        print("\nChecking manuscript completeness...")
        
        # Extract all table and figure environments from LaTeX
        table_environments = re.findall(r'\\begin\{table\}.*?\\end\{table\}', self.tex_content, re.DOTALL)
        figure_environments = re.findall(r'\\begin\{figure\}.*?\\end\{figure\}', self.tex_content, re.DOTALL)
        
        print(f"LaTeX manuscript contains:")
        print(f"  - {len(table_environments)} table environments")
        print(f"  - {len(figure_environments)} figure environments")
        
        # Count extracted files
        extracted_tables = len([f for f in os.listdir(self.extracted_dir) if f.startswith('Table') and f.endswith('.docx')])
        extracted_figures = len(set([re.match(r'Figure\s+(\d+)', f).group(1) for f in os.listdir(self.extracted_dir) 
                                   if f.startswith('Figure') and not f.endswith('.docx') and re.match(r'Figure\s+(\d+)', f)]))
        
        print(f"Extracted files:")
        print(f"  - {extracted_tables} table files")
        print(f"  - {extracted_figures} figure groups")
        
        # Check completeness
        if len(table_environments) == extracted_tables:
            print("✅ Table extraction complete")
        else:
            print(f"❌ Table mismatch: {len(table_environments)} in manuscript vs {extracted_tables} extracted")
        
        if len(figure_environments) == extracted_figures:
            print("✅ Figure extraction complete")
        else:
            print(f"❌ Figure mismatch: {len(figure_environments)} in manuscript vs {extracted_figures} extracted")
    
    def generate_submission_checklist(self):
        """Generate a submission checklist for the journal."""
        print("\nGenerating submission checklist...")
        
        checklist = []
        checklist.append("# Journal Submission Checklist")
        checklist.append("")
        checklist.append("## Tables")
        
        table_files = sorted([f for f in os.listdir(self.extracted_dir) if f.startswith('Table') and f.endswith('.docx')])
        for table_file in table_files:
            checklist.append(f"- [ ] {table_file} - Verify content matches manuscript")
        
        checklist.append("")
        checklist.append("## Figures")
        
        figure_files = sorted([f for f in os.listdir(self.extracted_dir) 
                              if f.startswith('Figure') and not f.endswith('.docx')])
        for figure_file in figure_files:
            checklist.append(f"- [ ] {figure_file} - Verify quality and content")
        
        checklist.append("")
        checklist.append("## Quality Checks")
        checklist.append("- [ ] All table formatting preserved")
        checklist.append("- [ ] All figure resolution adequate (>300 DPI for print)")
        checklist.append("- [ ] File naming matches manuscript references exactly")
        checklist.append("- [ ] No missing tables or figures")
        checklist.append("- [ ] All files open correctly")
        checklist.append("- [ ] Caption text matches manuscript")
        
        checklist_path = self.extracted_dir / "SUBMISSION_CHECKLIST.md"
        with open(checklist_path, 'w') as f:
            f.write('\n'.join(checklist))
        
        print(f"✅ Checklist saved to: {checklist_path}")
    
    def run_full_check(self):
        """Run all quality assurance checks."""
        print("=" * 60)
        print("QUALITY ASSURANCE CHECK FOR EXTRACTED FILES")
        print("=" * 60)
        
        # Check references
        table_count, missing_tables, extra_tables = self.check_table_references()
        figure_count, missing_figures = self.check_figure_references()
        
        # Check file quality
        self.check_file_quality()
        
        # Check completeness
        self.check_manuscript_completeness()
        
        # Generate checklist
        self.generate_submission_checklist()
        
        print("\n" + "=" * 60)
        print("QUALITY ASSURANCE SUMMARY")
        print("=" * 60)
        print(f"Tables extracted: {table_count}")
        print(f"Figures extracted: {figure_count}")
        
        if not missing_tables and not extra_tables and not missing_figures:
            print("✅ ALL CHECKS PASSED - Files ready for submission!")
        else:
            print("⚠️  ISSUES FOUND - Please review the above output")
        
        print("\nNext steps:")
        print("1. Review the SUBMISSION_CHECKLIST.md file")
        print("2. Manually verify table and figure content")
        print("3. Check file quality and resolution")
        print("4. Submit files to journal")

def main():
    """Main function to run quality assurance checks."""
    tex_file = "template.tex"
    extracted_dir = "extracted_submission_files"
    
    if not os.path.exists(tex_file):
        print(f"Error: {tex_file} not found!")
        return
    
    if not os.path.exists(extracted_dir):
        print(f"Error: {extracted_dir} not found!")
        return
    
    checker = QualityAssuranceChecker(tex_file, extracted_dir)
    checker.run_full_check()

if __name__ == "__main__":
    main()
