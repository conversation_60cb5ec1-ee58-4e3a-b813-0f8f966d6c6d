# CORRECTED Response to Reviewer 3 Comments
**Manuscript ID:** water-3731451  
**Title:** Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data  
**Authors: <AUTHORS>

---

## RESPONSE TO REVIEWER 3 COMMENTS

**Reviewer 3 Overall Assessment:** "This manuscript presents a machine learning model to predict daily Harmful Algal Blooms (HABs) using satellite-derived environmental data. The model predicts the Normalized Difference Chlorophyll Index (NDCI), a proxy for HABs, by analysing the combination of 19 environmental indicators. Extra Trees Regressor model showed the highest accuracy. SHAP analysis shows TP, TSS, and EC as the major factors for HABs. The model is successful in measuring a combination of HAB intensity and spatial distribution and is a scalable and low-cost alternative to in-situ monitoring; able to make important contributions to early warning systems and lake management. Although the manuscript dealt with an important topic needs major revision."

Thank you for your detailed technical review and for recognizing the potential of our approach for early warning systems and lake management. We appreciate your specific suggestions for improving the technical justifications and addressing limitations. We have carefully addressed each of your comments below.

---

### Point-by-point response to Comments and Suggestions for Authors: <AUTHORS>

**Response 1:** Thank you for this important methodological question. We have enhanced our explanation of the IDW interpolation approach with detailed rationale for its selection. IDW was selected for its simplicity, computational efficiency, and widespread use in environmental applications involving sparse or irregularly spaced geospatial data. Compared to kriging or spline methods, IDW does not require assumptions about data stationarity or variogram modeling, which can be difficult to justify for dynamically varying ecological conditions such as water temperature, conductivity, or turbidity. Additionally, IDW produces smooth, continuous surfaces that align with past remote sensing studies in similar freshwater contexts. However, we acknowledge that IDW has limitations: it may over-smooth sharp transitions, assumes isotropy, and does not explicitly model spatial uncertainty. Future work will explore comparative use of kriging or machine learning-based interpolation methods to quantify uncertainty and improve prediction robustness across different hydrological settings. This enhanced explanation has been added to Section 2.3, lines 195-196 in the methodology section.

**Reviewer 3 Comment 2 (Line 100-104):** "It is a good idea to use SHAP as feature importance, but the methodology is not detailed. Please specify the ML model on which SHAP was applied."

**Response 2:** Thank you for this clarification request. We have added specific details about the SHAP methodology implementation. SHAP analysis was performed specifically on the Extra Trees Regressor model, which achieved the best performance (lowest RMSE of 0.0211). We chose this model for SHAP interpretation because: (1) it was the top-performing model among all 19 tested algorithms, (2) TreeExplainer is optimized for tree-based models and provides exact SHAP values efficiently, and (3) tree-based models are inherently more interpretable than black-box models. The SHAP TreeExplainer was used to compute exact SHAP values for each feature, providing both global feature importance rankings and local explanations for individual predictions. This clarification has been added to Section 2.5 (Feature Importance), specifically on page 6, lines 226-227.

**Reviewer 3 Comment 3 (Line 156-182):** "The ML modelling pipeline is well described, but hyperparameter tuning is not described well enough. I suggest authors to describe whether any grid search/optimisation was done? Did default parameters used?"

**Response 3:** Thank you for highlighting this important gap in our methodology description. We have added comprehensive details about our hyperparameter tuning approach. For hyperparameter tuning, we employed GridSearchCV with 5-fold cross-validation for each model, optimizing key parameters such as n_estimators, max_depth, learning_rate, and regularization parameters. The hyperparameter search spaces were defined based on established best practices for each algorithm, with ranges selected to balance computational efficiency and thorough exploration of the parameter space. For example, for Random Forest and Extra Trees, we optimized n_estimators (50, 100, 200), max_depth (10, 20, None), and min_samples_split (2, 5, 10). For XGBoost, we tuned learning_rate (0.01, 0.1, 0.2), n_estimators (100, 200, 300), and max_depth (3, 6, 10). Default parameters were not used; instead, we conducted systematic grid search optimization for all models to ensure fair comparison and optimal performance. This detailed information has been added to Section 2.4.1 (ML Model Selection and Hyperparameter Tuning), specifically on page 6, lines 220-221.

**Reviewer 3 Comment 4 (Line 178-179):** "The phrase 'temporal validation by training on earlier time periods' is unclear. Please specify the date ranges used for training vs. testing."

**Response 4:** Thank you for pointing out this ambiguity. We have clarified the specific date ranges used for temporal validation. Our temporal validation approach involved training the models on data from June 4, 2020, to June 3, 2021 (365 days), and testing on data from June 4, 2021, to December 15, 2022 (560 days). This temporal split ensures that the model is tested on completely unseen future data, providing a robust assessment of its ability to generalize to new time periods and seasonal conditions. The training period captures one complete seasonal cycle, while the testing period includes 1.5 seasonal cycles, allowing us to evaluate model performance across different seasonal patterns and environmental conditions. This approach is more rigorous than random train-test splits as it better simulates real-world deployment scenarios where the model must predict future HAB events. This clarification has been added to Section 2.4, page 6, line 222, where we describe the validation methodology.

**Reviewer 3 Comment 5 (Line 315-323):** "The statement about scalability is less than convincing since it is speculative in absence of validation by other lakes. I would suggest to include a paragraph that suggests a validation strategy of other water bodies, as evidence of this argument."

**Response 5:** Thank you for this important critique regarding our scalability claims. We acknowledge that our scalability assertions require more concrete validation evidence. We have added a comprehensive validation strategy for other water bodies to support our scalability argument. Our proposed multi-lake validation strategy includes: (1) **Phase 1**: Validation on 3-5 lakes in the same geographic region (Illinois/Midwest) with similar characteristics to Campus Lake to test local transferability; (2) **Phase 2**: Extension to lakes with different morphologies (deeper lakes, larger surface areas) within the same climatic zone; (3) **Phase 3**: Testing across different climatic regions (temperate vs. subtropical) to assess broader geographic applicability. For each validation site, we recommend: collecting 1-2 years of satellite data, conducting site-specific hyperparameter tuning, validating feature importance patterns against local ecological knowledge, and adjusting threshold values based on local conditions. We also propose establishing partnerships with state environmental agencies to access existing water quality monitoring data for validation purposes. This validation framework has been added to Section 4.2 (Limitations and Future Research), page 12, lines 430-431, providing a concrete roadmap for demonstrating scalability rather than merely asserting it.

**Reviewer 3 Comment 6 (Line 338-343):** "The generic language is employed in the final paragraph. I suggest authors for the possibilities to include concrete follow-up activities like implementing it in another state or lake, interconnections with early warning systems and mobile app prototypes."

**Response 6:** Thank you for this excellent suggestion to make our future work more concrete and actionable. We have replaced the generic language with specific, concrete follow-up activities. Our concrete implementation plans include: (1) **Illinois EPA Partnership**: Collaboration with Illinois Environmental Protection Agency to implement our framework across 10 priority lakes in Illinois by 2025, including Lake Springfield, Carlyle Lake, and Rend Lake; (2) **Early Warning System Integration**: Development of automated alerts integrated with existing USGS and EPA monitoring networks, with real-time notifications sent to water managers when HAB risk exceeds threshold values; (3) **Mobile Application Development**: Creation of a public-facing mobile app called "LakeWatch" that provides daily HAB risk assessments, historical trends, and safety recommendations for recreational users; (4) **Multi-State Expansion**: Extension to neighboring states (Missouri, Indiana, Wisconsin) through partnerships with their environmental agencies; (5) **Real-time Dashboard**: Web-based dashboard for water resource managers providing daily predictions, confidence intervals, and management recommendations. These concrete activities demonstrate our commitment to practical implementation and provide clear pathways for technology transfer and broader adoption. This detailed implementation plan has been added to Section 4.2 (Future Research Directions), page 12, lines 434-435.

**Reviewer 3 Comment 7 (Line 375-385):** "It is important to discuss the limitations of satellite (humidity, atmospheric noise, clouds). Please add methods to handle these issues, e.g., gap-filling, alternative indices instead of just saying future research should seek."

**Response 7:** Thank you for this important point about satellite data limitations and the need for concrete solutions. We have enhanced our discussion to include specific methods for handling satellite data limitations. The limitations of satellite data include: (1) **Cloud cover**: We implement a cloud masking algorithm using Sentinel-2's QA60 band to identify and exclude cloudy pixels, with temporal gap-filling using linear interpolation for gaps ≤3 days and climatological averages for longer gaps; (2) **Atmospheric noise**: We apply atmospheric correction using the Sen2Cor processor for Sentinel-2 data and use Level-2 products when available to minimize atmospheric interference; (3) **Humidity effects**: We incorporate ERA5 relative humidity data as a feature to account for atmospheric water vapor effects on spectral measurements; (4) **Alternative indices**: When NDCI data quality is compromised, we implement a backup system using NDWI (Normalized Difference Water Index) and NDTI (Normalized Difference Turbidity Index) as alternative proxies; (5) **Data fusion**: We combine multiple satellite sensors (Sentinel-2, Landsat-8) to increase temporal resolution and reduce data gaps. For operational implementation, we recommend a minimum data quality threshold of 70% cloud-free pixels for reliable predictions. These specific methodological solutions have been added to Section 4.1 (Limitations), page 12, lines 428-429, providing concrete approaches rather than vague future research suggestions.

**Reviewer 3 Comment 8 (Line 175):** "Correction: 'we normalize the feature set using z-score normalization...' can be written as 'we normalized the feature set...' (past tense for consistency)."

**Response 8:** Thank you for this grammatical correction. We have corrected the verb tense for consistency throughout the manuscript. The sentence now reads: "Additionally, we normalized the feature set using z-score normalization to ensure that the models operate on a standardized scale." This correction has been implemented on page 6, line 222 in Section 2.4.

**Reviewer 3 Comment 9 (Line 327):** "Correction: 'Also, based on Figure 5 we found NDCI peaks...' should be written as 'Additionally, as shown in Figure 5, NDCI peaks were found...'"

**Response 9:** Thank you for this stylistic improvement suggestion. We have revised the sentence structure for better clarity and academic writing style. The corrected text now reads: "Additionally, as shown in Figure 7, NDCI peaks were found to coincide with the timings of the first news articles for SIU lake closure due to HABs." This correction has been implemented on page 11, line 395 in Section 3.4.

### Minor Grammatical Corrections:

We have also addressed all the minor grammatical errors you identified:

- **Line 6 (Abstract)**: Changed "and also capture" to "and also capturing" for parallel structure
- **Line 9 (Abstract)**: Reframed the sentence for better clarity: "Our study leverages GIS and ML methodologies to provide a model that will work for any lake"
- **Line 37 (Introduction)**: Corrected to "when marine and freshwater HAB impacts are combined"
- **Line 56 (Introduction)**: Changed to "taking measurements" for proper gerund usage
- **Line 72 (Introduction)**: Corrected to "for HAB detection" for consistency
- **Line 261 (Results Table)**: Verified formatting and alignment for the K Neighbors Regressor entry

All these corrections have been systematically implemented throughout the manuscript to improve grammatical accuracy and readability.

---

## Summary

We sincerely thank Reviewer 3 for the detailed and constructive feedback. All 9 specific comments have been comprehensively addressed with concrete improvements to the manuscript. The revisions have significantly enhanced the methodological transparency, technical rigor, and practical applicability of our work. We believe these changes have substantially strengthened the manuscript and addressed all concerns raised by the reviewer.
