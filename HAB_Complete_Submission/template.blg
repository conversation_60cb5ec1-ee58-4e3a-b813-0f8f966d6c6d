This is BibTeX, Version 0.99d (TeX Live 2022/dev/Debian)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: template.aux
The style file: Definitions/mdpi.bst
Database file #1: reference.bib
You've used 92 entries,
            2887 wiz_defined-function locations,
            1140 strings with 25249 characters,
and the built_in function-call counts, 38876 in all, are:
= -- 3316
> -- 2510
< -- 58
+ -- 1115
- -- 682
* -- 3351
:= -- 5987
add.period$ -- 288
call.type$ -- 92
change.case$ -- 93
chr.to.int$ -- 92
cite$ -- 92
duplicate$ -- 1707
empty$ -- 3067
format.name$ -- 865
if$ -- 8136
int.to.chr$ -- 1
int.to.str$ -- 92
missing$ -- 85
newline$ -- 473
num.names$ -- 273
pop$ -- 831
preamble$ -- 0
purify$ -- 92
quote$ -- 0
skip$ -- 1199
stack$ -- 0
substring$ -- 1890
swap$ -- 382
text.length$ -- 14
text.prefix$ -- 1
top$ -- 0
type$ -- 625
warning$ -- 0
while$ -- 313
width$ -- 0
write$ -- 1154
