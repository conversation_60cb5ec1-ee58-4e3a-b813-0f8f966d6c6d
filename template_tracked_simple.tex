%  LaTeX support: <EMAIL>
%  For support, please attach all files needed for compiling as well as the log file, and specify your operating system, LaTeX version, and LaTeX editor.

%=================================================================
\documentclass[water,article,submit,pdftex,moreauthors]{Definitions/mdpi}

%\usepackage{biblatex}
\usepackage{subfig}
\usepackage{xcolor}
\usepackage{soul}

% Define colors for tracked changes
\definecolor{addedtext}{RGB}{0,100,0}
\definecolor{deletedtext}{RGB}{200,0,0}
\definecolor{changedtext}{RGB}{0,0,200}

% Define commands for tracked changes
\newcommand{\added}[1]{\textcolor{addedtext}{\textbf{[ADDED: #1]}}}
\newcommand{\deleted}[1]{\textcolor{deletedtext}{\sout{#1}}}
\newcommand{\changed}[2]{\textcolor{deletedtext}{\sout{#1}} \textcolor{changedtext}{\textbf{#2}}}

%\addbibresource{reference.bib}

%=================================================================
% Add the class options to the \documentclass command according to the journal that you are submitting to:
% water, sustainability, remotesensing, resources, healthcare, ijerph, jpm, jfmk, forests, energies, agriculture, pathogens
% Some journals require a specific calls for papers:
% sustainability, membranes, antioxidants, jfmk, jpm, pharmaceutics
% example: \documentclass[sustainability,article,submit,moreauthors,pdftex]{mdpi}

% Is a journal abbreviation provided in the template
% Please provide the journal abbreviation here if applicable:

% The class option "submit" will be changed to "accept" by the Editorial Office when the paper is accepted.
% This will only make changes to the frontpage (e.g., the logo of the journal will get visible), the headings, and the copyright information.
% Also, line numbers will be removed.
% Please do not add the option "accept" yourself.

% For citations and references, please use the natbib package with the provided "mdpi.bst".
% To activate the natbib package, uncomment the following line:
%\usepackage[square,numbers,sort&compress]{natbib}
% Note: the option is "round" (which is the default), "square" or "angle" for the shape of the brackets.
% The option "numbers" is for the numerical references. Please switch to "authoryear" for the name-year system.
% The option "sort&compress" is for the automatic sorting and compression of the numerical references.

%=================================================================
% Please use the following mathematics environments: Theorem, Lemma, Corollary, Proposition, Characterization, Property, Problem, Example, ExamplesandDefinitions, Hypothesis, Remark, Definition, Notation, Assumption
%% For proofs, please use the proof environment (the amsthm package is loaded by the MDPI class).

%=================================================================
% Full title of the paper (Capitalized)
\Title{Daily Prediction Model for \changed{Harmful Algae Blooms}{Harmful Algal Blooms} (HABs) using geospatial satellite data}

% MDPI internal command: Title for citation in the left column
\TitleCitation{Title}

% Author Orchid ID: enter ID or remove command
\newcommand{\orcidauthorA}{0000-0000-0000-000X} % Add \orcidA{} behind the author's name
%\newcommand{\orcidauthorB}{0000-0000-0000-000X} % Add \orcidB{} behind the author's name

% Authors, for the paper (add full first names)
\Author{Vatsal Mitesh Tailor $^{1,\dagger,\ddagger}$\orcidA{}, Anuj Tiwari $^{2,\ddagger}$ and Marcia R. Silva $^{2,\ddagger}$}

% MDPI internal command: Authors, for metadata in PDF
\AuthorNames{Vatsal Mitesh Tailor, Anuj Tiwari, Marcia R. Silva}

% MDPI internal command: Authors, for citation in the left column
\AuthorCitation{Tailor, V.M.; Tiwari, A.; Silva, M.R.}
% If this is a Chicago style journal: Lastname, Firstname, Firstname Lastname, and Firstname Lastname.

% Affiliations / Addresses (Add [1] after \address if there is only one affiliation.)
\address{%
$^{1}$ \quad Department of Computer Science, University of Illinois at Urbana-Champaign, Urbana, IL 61801, USA\\
$^{2}$ \quad Department of Plant, Soil and Agricultural Systems, Southern Illinois University, Carbondale, IL 62901, USA}

% Contact information of the corresponding author
\corres{Correspondence: <EMAIL>}

% Current address and/or shared authorship
\firstnote{Current address: Department of Computer Science, University of Illinois at Urbana-Champaign, Urbana, IL 61801, USA} 
\secondnote{These authors contributed equally to this work.}
% The commands \thirdnote{} till \eighthnote{} are available for further notes

%\simplesumm{} % Simple summary

%\conference{} % An extended version of a conference paper

% Abstract (Do not insert blank lines, i.e. \\) 
\abstract{\changed{Harmful Algae Blooms}{Harmful Algal Blooms} (HABs) pose significant environmental and public health risks to freshwater ecosystems worldwide. This study presents a comprehensive \added{machine learning (ML)} approach for daily HAB prediction using geospatial satellite data from Campus Lake at Southern Illinois University. \changed{We utilized twelve features}{We collected 19 environmental features from satellite and climate data sources, from which we selected 12 key predictive features through feature importance analysis} derived from Sentinel-2, Sentinel-5P, and ERA5 datasets to predict the Normalized Difference Chlorophyll Index (NDCI) as a proxy for HAB occurrence. Our methodology employed 19 different \changed{machine learning}{ML} algorithms, with Random Forest achieving the highest performance (R² = 0.89, RMSE = 0.032). \added{SHapley Additive exPlanations (SHAP)} analysis revealed that Total Phosphorous (TP), Total Suspended Solids (TSS), Electric Conductivity (EC), \changed{Land Surface Temperature Night (LST\_Night)}{Land Surface Temperature Day (LST\_Day)}, and Carbon Monoxide (CO) are the most significant predictors. The model demonstrates strong predictive capability with actionable threshold values for management interventions: TP > 0.15 mg/L, \changed{LST\_Night}{LST\_Day} > 25°C, TSS > 30 mg/L, EC > 400 μS/cm, and CO > 1.5 mg/m³. \deleted{Thus, traditional laboratory methodology cannot capture spatiotemporal variations in HAB across the water body.} This research provides a scalable framework for real-time HAB monitoring and early warning systems in freshwater environments, contributing to improved water quality management and public health protection.}

% Keywords
\keyword{Harmful Algal Blooms; \changed{machine learning}{Machine Learning}; NDCI; Satellite Remote Sensing; Water Quality; SHAP Analysis; Environmental Monitoring}

% The fields PACS, MSC, and JEL may be left empty or commented out if not applicable
%\PACS{J0101}
%\MSC{}
%\JEL{}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Only for the journal Diversity
%\LSID{\url{http://}}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Only for the journal Applied Sciences:
%\featuredapplication{Authors are encouraged to provide a concise description of the specific application or a potential application of the work. This section is not mandatory.}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Only for the journal Data:
%\dataset{DOI number or link to the deposited data set in cases where the data set is published or set to be published separately. If the data set is submitted and will be published as a supplement to this paper in the journal Data, this field will be filled by the editors of the journal. In this case, please make sure to submit the data set as a supplement when entering your manuscript into our manuscript submission system.}

%\datasetlicense{license under which the data set is made available (CC0, CC-BY, CC-BY-SA, CC-BY-NC, etc.)}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Only for the journal Toxins
%\keycontribution{The breakthroughs or highlights of the manuscript. Authors can write one or two sentences to describe the most important part of the paper.}

%\setcounter{secnumdepth}{4}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Only for the journal Forests:
%\reviewreports{\\
%Reviewer 1 comments and authors' response\\
%Reviewer 2 comments and authors' response\\
%Reviewer 3 comments and authors' response
%}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{Introduction}

\changed{Harmful Algae Blooms}{Harmful Algal Blooms} (HABs) represent one of the most pressing environmental challenges facing freshwater ecosystems globally \cite{paerl2008blooms}. These phenomena occur when certain species of algae, particularly cyanobacteria, proliferate rapidly under favorable environmental conditions, often producing toxins that pose serious risks to aquatic life, human health, and economic activities \cite{chorus2021toxic}. \added{The economic impact of HABs is substantial, with freshwater HABs alone causing approximately \$62 million in annual damages in the United States, while total HAB-related costs reach \$82 million annually when including both marine and freshwater systems \cite{lopez2008economic}.}

The formation and persistence of HABs are influenced by a complex interplay of environmental factors, including nutrient availability (particularly nitrogen and phosphorus), water temperature, light conditions, pH levels, and hydrodynamic processes \cite{glibert2011role}. \added{Traditional monitoring approaches rely heavily on in-situ sampling and laboratory analysis, which, while accurate, are limited in their spatial and temporal coverage.} Remote sensing technology has emerged as a powerful tool for monitoring and predicting HABs, offering the capability to observe large water bodies with high temporal frequency and spatial resolution \cite{mishra2012normalized}.

\added{This study addresses a critical gap in HAB prediction research by developing a comprehensive ML framework specifically designed for daily HAB forecasting in freshwater systems. While previous studies have focused primarily on marine environments or used limited feature sets, our approach integrates multiple satellite data sources and employs advanced feature selection techniques to identify the most predictive environmental variables. The novelty of our work lies in: (1) the integration of multi-source satellite data (Sentinel-2, Sentinel-5P, ERA5) for comprehensive environmental characterization, (2) the application of SHAP analysis for interpretable feature importance ranking, and (3) the development of actionable threshold values for practical water management applications.}

The Normalized Difference Chlorophyll Index (NDCI) has been established as an effective proxy for chlorophyll-a concentration and, consequently, for HAB detection in inland waters \cite{mishra2012normalized}. \added{While NDCI is less frequently used compared to other indices in marine environments, it has proven particularly effective for turbid freshwater systems where traditional blue-green algorithms may fail due to high suspended sediment concentrations and complex optical properties.} The NDCI leverages the spectral characteristics of chlorophyll absorption and reflection, making it particularly suitable for detecting algal biomass in optically complex inland water bodies.

\added{Machine learning (ML)} approaches have shown considerable promise in environmental monitoring applications, offering the ability to identify complex, non-linear relationships between environmental variables and ecological outcomes \cite{peterson2008machine}. \added{Recent advances in ML applications to HAB prediction have demonstrated the potential for improved accuracy over traditional statistical methods, particularly when dealing with high-dimensional environmental datasets \cite{ho2019machine, kim2014machine}.} The integration of \added{ML} techniques with remote sensing data provides an opportunity to develop robust, scalable prediction models that can support real-time monitoring and early warning systems.

This study aims to develop and evaluate a comprehensive \added{ML} framework for daily HAB prediction using multi-source satellite data. \added{Our specific objectives are: (1) to integrate environmental data from multiple satellite sources (Sentinel-2, Sentinel-5P, ERA5) for comprehensive HAB prediction, (2) to evaluate the performance of 19 different ML algorithms for NDCI prediction, (3) to identify the most important environmental predictors using SHAP analysis, and (4) to develop actionable threshold values for water management applications.} The research focuses on Campus Lake at Southern Illinois University, providing a controlled case study environment for model development and validation.

\section{Materials and Methods}

\subsection{Study Area}

Campus Lake at Southern Illinois University (SIU) Carbondale serves as the primary study site for this research. Located in southern Illinois, USA (37.7167°N, 89.2167°W), the lake covers approximately 40 hectares and has a maximum depth of 4.5 meters \cite{panno2006hydrogeology}. The lake is characterized by its shallow depth, high nutrient loading from surrounding urban and agricultural activities, and frequent HAB occurrences during warm months, making it an ideal location for HAB prediction model development.

The lake's hydrological characteristics include seasonal stratification patterns, with thermal stratification typically occurring from late spring through early fall. Nutrient inputs primarily originate from stormwater runoff, groundwater discharge, and atmospheric deposition. The lake has experienced recurring HAB events, particularly during summer months when water temperatures exceed 25°C and nutrient concentrations are elevated.

\subsection{Data Collection and Processing}

\subsubsection{Satellite Data Sources}

\added{We collected 19 environmental features from three primary satellite and climate data sources, from which we selected 12 key predictive features through feature importance analysis.} The analysis incorporates multiple remote sensing platforms to capture comprehensive environmental conditions:

\textbf{Sentinel-2 Data:} High-resolution multispectral imagery was obtained from the Sentinel-2 constellation, providing 10-20 meter spatial resolution data with a 5-day revisit frequency. Seven water quality parameters were calculated from Sentinel-2 spectral bands, as detailed in Table \ref{tab2}. \added{Sentinel-2 spectral bands are designated as B3 (Green, 560 nm), B4 (Red, 665 nm), B5 (Red Edge, 705 nm), B8 (Near Infrared, 842 nm), B11 (Short Wave Infrared 1, 1610 nm), and B12 (Short Wave Infrared 2, 2190 nm).}

\textbf{Sentinel-5P Data:} Atmospheric composition data, including trace gas concentrations, were acquired from the Sentinel-5P TROPOMI instrument, providing daily global coverage at 7×3.5 km spatial resolution.

\textbf{ERA5 Reanalysis Data:} Meteorological variables were obtained from the European Centre for Medium-Range Weather Forecasts (ECMWF) ERA5 reanalysis dataset, providing hourly atmospheric data at 0.25° spatial resolution.

Table \ref{tab1} outlines the 19 distinct remote sensing parameters collected from ERA5 \cite{era5}, Sentinel-5P \cite{sentinel5}, and Sentinel-2 \cite{sentinel2}, each contributing unique spatial insights to the dataset. \added{All 19 environmental features are used in our ML models to predict HAB formation.} The analysis incorporates seven water quality parameters calculated from Sentinel-2 spectral bands, as detailed in Table \ref{tab2}.

% Continue with rest of document...
\end{document}
