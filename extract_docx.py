import xml.etree.ElementTree as ET

tree = ET.parse('docx_extracted/word/document.xml')
root = tree.getroot()
ns = '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}'
text = ''

for paragraph in root.findall('.//' + ns + 'p'):
    para_text = ''
    for run in paragraph.findall('.//' + ns + 't'):
        if run.text:
            para_text += run.text
    text += para_text + '\n'

print(text)
