#!/usr/bin/env python3
"""
Extract all tables and figures from LaTeX manuscript for journal submission.
Creates separate Word documents for tables and high-resolution image files for figures.
"""

import re
import os
import shutil
from pathlib import Path
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
import pandas as pd
from PIL import Image
import subprocess

class LaTeXExtractor:
    def __init__(self, tex_file_path):
        self.tex_file_path = tex_file_path
        self.output_dir = Path("extracted_submission_files")
        self.output_dir.mkdir(exist_ok=True)
        
        # Read the LaTeX file
        with open(tex_file_path, 'r', encoding='utf-8') as f:
            self.tex_content = f.read()
    
    def extract_tables(self):
        """Extract all tables from LaTeX and save as Word documents."""
        print("Extracting tables from LaTeX manuscript...")
        
        # Pattern to match table environments
        table_pattern = r'\\begin\{table\}.*?\\end\{table\}'
        tables = re.findall(table_pattern, self.tex_content, re.DOTALL)
        
        table_count = 0
        for table_content in tables:
            table_count += 1
            
            # Extract caption and label
            caption_match = re.search(r'\\caption\{(.*?)\}', table_content, re.DOTALL)
            label_match = re.search(r'\\label\{(.*?)\}', table_content)
            
            caption = caption_match.group(1) if caption_match else f"Table {table_count}"
            label = label_match.group(1) if label_match else f"tab{table_count}"
            
            # Clean caption text
            caption = self.clean_latex_text(caption)
            
            # Extract tabular content
            tabular_match = re.search(r'\\begin\{tabular.*?\}(.*?)\\end\{tabular\}', table_content, re.DOTALL)
            if not tabular_match:
                tabular_match = re.search(r'\\begin\{tabularx.*?\}(.*?)\\end\{tabularx\}', table_content, re.DOTALL)
            
            if tabular_match:
                tabular_content = tabular_match.group(1)
                self.create_word_table(tabular_content, caption, table_count)
            else:
                print(f"Warning: Could not extract tabular content for Table {table_count}")
        
        print(f"Extracted {table_count} tables")
        return table_count
    
    def extract_figures(self):
        """Extract all figures from LaTeX and copy/convert to high-resolution formats."""
        print("Extracting figures from LaTeX manuscript...")
        
        # Pattern to match figure environments
        figure_pattern = r'\\begin\{figure\}.*?\\end\{figure\}'
        figures = re.findall(figure_pattern, self.tex_content, re.DOTALL)
        
        figure_count = 0
        for figure_content in figures:
            figure_count += 1
            
            # Extract includegraphics commands
            graphics_matches = re.findall(r'\\includegraphics(?:\[.*?\])?\{(.*?)\}', figure_content)
            
            # Extract caption and label
            caption_match = re.search(r'\\caption\{(.*?)\}', figure_content, re.DOTALL)
            label_match = re.search(r'\\label\{(.*?)\}', figure_content)
            
            caption = caption_match.group(1) if caption_match else f"Figure {figure_count}"
            label = label_match.group(1) if label_match else f"fig{figure_count}"
            
            # Clean caption text
            caption = self.clean_latex_text(caption)
            
            # Process each image in the figure
            for i, image_path in enumerate(graphics_matches):
                self.process_figure_image(image_path, figure_count, i, caption)
        
        print(f"Extracted {figure_count} figures")
        return figure_count
    
    def clean_latex_text(self, text):
        """Clean LaTeX commands from text."""
        # Remove common LaTeX commands
        text = re.sub(r'\\[a-zA-Z]+\{([^}]*)\}', r'\1', text)
        text = re.sub(r'\\[a-zA-Z]+', '', text)
        text = re.sub(r'\{|\}', '', text)
        text = re.sub(r'\\\\', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def create_word_table(self, tabular_content, caption, table_num):
        """Create a Word document with the table."""
        doc = Document()
        
        # Add title
        title = doc.add_heading(f'Table {table_num}', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add caption
        caption_para = doc.add_paragraph(caption)
        caption_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Parse tabular content
        rows = self.parse_tabular_content(tabular_content)
        
        if rows:
            # Create table in Word
            table = doc.add_table(rows=len(rows), cols=len(rows[0]))
            table.style = 'Table Grid'
            
            # Fill table data
            for i, row_data in enumerate(rows):
                for j, cell_data in enumerate(row_data):
                    if j < len(table.rows[i].cells):
                        table.rows[i].cells[j].text = self.clean_latex_text(cell_data)
        
        # Save document
        output_path = self.output_dir / f"Table {table_num}.docx"
        doc.save(output_path)
        print(f"Created: {output_path}")
    
    def parse_tabular_content(self, content):
        """Parse LaTeX tabular content into rows and columns."""
        # Remove LaTeX table formatting commands
        content = re.sub(r'\\toprule|\\midrule|\\bottomrule', '', content)
        content = re.sub(r'\\hline', '', content)
        content = re.sub(r'\\newcolumntype\{.*?\}\{.*?\}', '', content)
        
        # Split into rows
        rows = []
        lines = content.split('\\\\')
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('\\'):
                # Split by & to get columns
                columns = [col.strip() for col in line.split('&')]
                if columns and any(col for col in columns):  # Skip empty rows
                    rows.append(columns)
        
        return rows
    
    def process_figure_image(self, image_path, figure_num, sub_num, caption):
        """Process and copy figure images to output directory."""
        # Remove file extension from path for searching
        base_name = os.path.splitext(image_path)[0]
        
        # Common image extensions to search for
        extensions = ['.jpg', '.jpeg', '.png', '.pdf', '.eps', '.tiff', '.tif']
        
        source_file = None
        for ext in extensions:
            potential_path = base_name + ext
            if os.path.exists(potential_path):
                source_file = potential_path
                break
        
        if not source_file:
            print(f"Warning: Could not find image file for {image_path}")
            return
        
        # Determine output filename
        if sub_num == 0:
            output_name = f"Figure {figure_num}"
        else:
            output_name = f"Figure {figure_num}_{chr(97 + sub_num)}"  # a, b, c, etc.
        
        # Get file extension
        _, ext = os.path.splitext(source_file)
        
        # Copy file to output directory
        output_path = self.output_dir / f"{output_name}{ext}"
        shutil.copy2(source_file, output_path)
        
        # If it's a PDF, also create a high-resolution PNG
        if ext.lower() == '.pdf':
            self.convert_pdf_to_png(output_path, output_name)
        
        print(f"Copied: {output_path}")
    
    def convert_pdf_to_png(self, pdf_path, base_name):
        """Convert PDF to high-resolution PNG."""
        try:
            png_path = self.output_dir / f"{base_name}.png"
            # Use ImageMagick to convert PDF to PNG at high resolution
            subprocess.run([
                'convert', '-density', '300', str(pdf_path), str(png_path)
            ], check=True)
            print(f"Converted to PNG: {png_path}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"Warning: Could not convert {pdf_path} to PNG. ImageMagick may not be installed.")
    
    def create_summary_document(self, table_count, figure_count):
        """Create a summary document listing all extracted files."""
        doc = Document()
        
        # Title
        title = doc.add_heading('Extracted Tables and Figures Summary', level=1)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Tables section
        doc.add_heading('Tables', level=2)
        doc.add_paragraph(f"Total tables extracted: {table_count}")
        
        for i in range(1, table_count + 1):
            doc.add_paragraph(f"• Table {i}.docx", style='List Bullet')
        
        # Figures section
        doc.add_heading('Figures', level=2)
        doc.add_paragraph(f"Total figures extracted: {figure_count}")
        
        # List all figure files in output directory
        figure_files = sorted([f for f in os.listdir(self.output_dir) 
                              if f.startswith('Figure') and not f.endswith('.docx')])
        
        for fig_file in figure_files:
            doc.add_paragraph(f"• {fig_file}", style='List Bullet')
        
        # Instructions
        doc.add_heading('Submission Instructions', level=2)
        instructions = [
            "1. All table files are in Microsoft Word (.docx) format",
            "2. All figure files are in high-resolution image formats (PNG, JPG, PDF)",
            "3. File names correspond exactly to the references in the manuscript",
            "4. Submit each file separately as required by the journal",
            "5. Verify that all content matches the original manuscript exactly"
        ]
        
        for instruction in instructions:
            doc.add_paragraph(instruction, style='List Number')
        
        # Save summary
        summary_path = self.output_dir / "SUBMISSION_SUMMARY.docx"
        doc.save(summary_path)
        print(f"Created summary: {summary_path}")

def main():
    """Main function to extract tables and figures."""
    tex_file = "template.tex"
    
    if not os.path.exists(tex_file):
        print(f"Error: {tex_file} not found!")
        return
    
    print("Starting extraction of tables and figures from LaTeX manuscript...")
    print("=" * 60)
    
    extractor = LaTeXExtractor(tex_file)
    
    # Extract tables
    table_count = extractor.extract_tables()
    print()
    
    # Extract figures
    figure_count = extractor.extract_figures()
    print()
    
    # Create summary
    extractor.create_summary_document(table_count, figure_count)
    
    print("=" * 60)
    print(f"Extraction complete!")
    print(f"Tables extracted: {table_count}")
    print(f"Figures extracted: {figure_count}")
    print(f"All files saved to: {extractor.output_dir}")
    print("\nFiles ready for journal submission!")

if __name__ == "__main__":
    main()
