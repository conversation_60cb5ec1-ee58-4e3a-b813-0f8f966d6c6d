#!/bin/bash

# Extract the case study section from template.tex
sed -n '/\\subsection{Case Study: HAB Drivers on June 24, 2022}/,/\\subsection{Management Implications and Recommendations}/p' template.tex > case_study.tex

# Extract the management implications section from template.tex
sed -n '/\\subsection{Management Implications and Recommendations}/,/\\section{Conclusions}/p' template.tex > management.tex

# Extract the conclusions section from template.tex
sed -n '/\\section{Conclusions}/,/%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%/p' template.tex > conclusions.tex

# Create a new template with the extracted sections
cat > new_template.tex << EOF
\\documentclass[water,article,submit,pdftex,moreauthors]{Definitions/mdpi}

\\usepackage{subfig}

\\firstpage{1} 
\\makeatletter 
\\setcounter{page}{\\@firstpage} 
\\makeatother
\\pubvolume{1}
\\issuenum{1}
\\articlenumber{0}
\\pubyear{2024}
\\copyrightyear{2024}
\\datereceived{ } 
\\daterevised{ }
\\dateaccepted{ } 
\\datepublished{ } 
\\hreflink{https://doi.org/} 

\\Title{Daily Prediction Model for Harmful Algae Blooms (HABs) using geospatial satellite data}

\\Author{Firstname Lastname \$^{1,\\dagger,\\ddagger}\$, Firstname Lastname \$^{2,\\ddagger}\$ and Firstname Lastname \$^{2,}\$*}

\\AuthorNames{Firstname Lastname, Firstname Lastname and Firstname Lastname}

\\AuthorCitation{Lastname, F.; Lastname, F.; Lastname, F.}

\\address{%
\$^{1}\$ \\quad Affiliation 1; <EMAIL>\\\\
\$^{2}\$ \\quad Affiliation 2; <EMAIL>}

\\corres{Correspondence: <EMAIL>}

\\firstnote{Current address: Affiliation.}
\\secondnote{These authors contributed equally to this work.}

\\abstract{This paper presents a machine learning approach for predicting Harmful Algal Blooms (HABs) using geospatial satellite data. We analyze data from Campus Lake at Southern Illinois University to identify key environmental factors driving HAB formation. Our case study of June 24, 2022, reveals how Total Phosphorus, Total Suspended Solids, and other parameters interact to create conditions conducive to HAB development. We provide management recommendations based on these findings, including targeted monitoring strategies and pre-emptive mitigation approaches.}

\\keyword{Harmful Algae Blooms, Geographic Information Systems, Machine Learning, Remote Detection} 

\\begin{document}

\\section{Introduction}
Harmful Algal Blooms (HABs) represent a significant environmental challenge with far-reaching implications for aquatic ecosystems, public health, and economic activities. This paper presents our findings on using machine learning to predict HAB formation using geospatial satellite data.

EOF

# Add the case study section
cat case_study.tex >> new_template.tex

# Add the management implications section
cat management.tex >> new_template.tex

# Add the conclusions section
cat conclusions.tex >> new_template.tex

# Add the end document command
echo "\\end{document}" >> new_template.tex

# Compile the new template
pdflatex new_template.tex

echo "Done!"
