%  LaTeX support: <EMAIL>
%  For support, please attach all files needed for compiling as well as the log file, and specify your operating system, LaTeX version, and LaTeX editor.

%=================================================================
\documentclass[water,article,submit,pdftex,moreauthors]{Definitions/mdpi}

%\usepackage{biblatex}
\usepackage{subfig}
\usepackage[final]{changes}
\usepackage{xcolor}

% Configure the changes package for tracked changes
\definechangesauthor[name={Reviewer Response}, color=blue]{reviewer}

%\addbibresource{reference.bib}

%=================================================================
% Add the class options to the \documentclass command according to the journal that you are submitting to:
% water, sustainability, remotesensing, resources, healthcare, ijerph, jpm, jfmk, forests, energies, agriculture, pathogens
% Some journals require a specific calls for papers:
% sustainability, membranes, antioxidants, jfmk, jpm, pharmaceutics
% example: \documentclass[sustainability,article,submit,moreauthors,pdftex]{mdpi}

% Is a journal abbreviation provided in the template
% Please provide the journal abbreviation here if applicable:

% The class option "submit" will be changed to "accept" by the Editorial Office when the paper is accepted.
% This will only make changes to the frontpage (e.g., the logo of the journal will get visible), the headings, and the copyright information.
% Also, line numbers will be removed.
% Please do not add the option "accept" yourself.

% For citations and references, please use the natbib package with the provided "mdpi.bst".
% To activate the natbib package, uncomment the following line:
%\usepackage[square,numbers,sort&compress]{natbib}
% Note: the option is "round" (which is the default), "square" or "angle" for the shape of the brackets.
% The option "numbers" is for the numerical references. Please switch to "authoryear" for the name-year system.
% The option "sort&compress" is for the automatic sorting and compression of the numerical references.

%=================================================================
% Please use the following mathematics environments: Theorem, Lemma, Corollary, Proposition, Characterization, Property, Problem, Example, ExamplesandDefinitions, Hypothesis, Remark, Definition, Notation, Assumption
%% For proofs, please use the proof environment (the amsthm package is loaded by the MDPI class).

%=================================================================
% Full title of the paper (Capitalized)
\Title{Daily Prediction Model for \replaced[id=reviewer]{Harmful Algal Blooms}{Harmful Algae Blooms} (HABs) using geospatial satellite data}

% MDPI internal command: Title for citation in the left column
\TitleCitation{Title}

% Author Orchid ID: enter ID or remove command
\newcommand{\orcidauthorA}{0000-0000-0000-000X} % Add \orcidA{} behind the author's name
%\newcommand{\orcidauthorB}{0000-0000-0000-000X} % Add \orcidB{} behind the author's name

% Authors, for the paper (add full first names)
\Author{Vatsal Mitesh Tailor $^{1,\dagger,\ddagger}$\orcidA{}, Anuj Tiwari $^{2,\ddagger}$ and Marcia R. Silva $^{2,\ddagger}$}

% MDPI internal command: Authors, for metadata in PDF
\AuthorNames{Vatsal Mitesh Tailor, Anuj Tiwari, Marcia R. Silva}

% MDPI internal command: Authors, for citation in the left column
\AuthorCitation{Tailor, V.M.; Tiwari, A.; Silva, M.R.}
% If this is a Chicago style journal: Lastname, Firstname, Firstname Lastname, and Firstname Lastname.

% Affiliations / Addresses (Add [1] after \address if there is only one affiliation.)
\address{%
$^{1}$ \quad Department of Computer Science, University of Illinois at Urbana-Champaign, Urbana, IL 61801, USA\\
$^{2}$ \quad Department of Plant, Soil and Agricultural Systems, Southern Illinois University, Carbondale, IL 62901, USA}

% Contact information of the corresponding author
\corres{Correspondence: <EMAIL>}

% Current address and/or shared authorship
\firstnote{Current address: Department of Computer Science, University of Illinois at Urbana-Champaign, Urbana, IL 61801, USA} 
\secondnote{These authors contributed equally to this work.}
% The commands \thirdnote{} till \eighthnote{} are available for further notes

%\simplesumm{} % Simple summary

%\conference{} % An extended version of a conference paper

% Abstract (Do not insert blank lines, i.e. \\) 
\abstract{\replaced[id=reviewer]{Harmful Algal Blooms}{Harmful Algae Blooms} (HABs) pose significant environmental and public health risks to freshwater ecosystems worldwide. This study presents a comprehensive \added[id=reviewer]{machine learning (ML)} approach for daily HAB prediction using geospatial satellite data from Campus Lake at Southern Illinois University. \replaced[id=reviewer]{We collected 19 environmental features from satellite and climate data sources, from which we selected 12 key predictive features through feature importance analysis}{We utilized twelve features} derived from Sentinel-2, Sentinel-5P, and ERA5 datasets to predict the Normalized Difference Chlorophyll Index (NDCI) as a proxy for HAB occurrence. Our methodology employed 19 different \replaced[id=reviewer]{ML}{machine learning} algorithms, with Random Forest achieving the highest performance (R² = 0.89, RMSE = 0.032). \added[id=reviewer]{SHapley Additive exPlanations (SHAP)} analysis revealed that Total Phosphorous (TP), Total Suspended Solids (TSS), Electric Conductivity (EC), \replaced[id=reviewer]{Land Surface Temperature Day (LST\_Day)}{Land Surface Temperature Night (LST\_Night)}, and Carbon Monoxide (CO) are the most significant predictors. The model demonstrates strong predictive capability with actionable threshold values for management interventions: TP > 0.15 mg/L, \replaced[id=reviewer]{LST\_Day}{LST\_Night} > 25°C, TSS > 30 mg/L, EC > 400 μS/cm, and CO > 1.5 mg/m³. \deleted[id=reviewer]{Thus, traditional laboratory methodology cannot capture spatiotemporal variations in HAB across the water body.} This research provides a scalable framework for real-time HAB monitoring and early warning systems in freshwater environments, contributing to improved water quality management and public health protection.}

% Keywords
\keyword{Harmful Algal Blooms; \replaced[id=reviewer]{Machine Learning}{machine learning}; NDCI; Satellite Remote Sensing; Water Quality; SHAP Analysis; Environmental Monitoring}

% The fields PACS, MSC, and JEL may be left empty or commented out if not applicable
%\PACS{J0101}
%\MSC{}
%\JEL{}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Only for the journal Diversity
%\LSID{\url{http://}}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Only for the journal Applied Sciences:
%\featuredapplication{Authors are encouraged to provide a concise description of the specific application or a potential application of the work. This section is not mandatory.}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Only for the journal Data:
%\dataset{DOI number or link to the deposited data set in cases where the data set is published or set to be published separately. If the data set is submitted and will be published as a supplement to this paper in the journal Data, this field will be filled by the editors of the journal. In this case, please make sure to submit the data set as a supplement when entering your manuscript into our manuscript submission system.}

%\datasetlicense{license under which the data set is made available (CC0, CC-BY, CC-BY-SA, CC-BY-NC, etc.)}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Only for the journal Toxins
%\keycontribution{The breakthroughs or highlights of the manuscript. Authors can write one or two sentences to describe the most important part of the paper.}

%\setcounter{secnumdepth}{4}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Only for the journal Forests:
%\reviewreports{\\
%Reviewer 1 comments and authors' response\\
%Reviewer 2 comments and authors' response\\
%Reviewer 3 comments and authors' response
%}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

\section{Introduction}

\replaced[id=reviewer]{Harmful Algal Blooms}{Harmful Algae Blooms} (HABs) represent one of the most pressing environmental challenges facing freshwater ecosystems globally \cite{paerl2008blooms}. These phenomena occur when certain species of algae, particularly cyanobacteria, proliferate rapidly under favorable environmental conditions, often producing toxins that pose serious risks to aquatic life, human health, and economic activities \cite{chorus2021toxic}. \added[id=reviewer]{The economic impact of HABs is substantial, with freshwater HABs alone causing approximately \$62 million in annual damages in the United States, while total HAB-related costs reach \$82 million annually when including both marine and freshwater systems \cite{lopez2008economic}.}

The formation and persistence of HABs are influenced by a complex interplay of environmental factors, including nutrient availability (particularly nitrogen and phosphorus), water temperature, light conditions, pH levels, and hydrodynamic processes \cite{glibert2011role}. \added[id=reviewer]{Traditional monitoring approaches rely heavily on in-situ sampling and laboratory analysis, which, while accurate, are limited in their spatial and temporal coverage.} Remote sensing technology has emerged as a powerful tool for monitoring and predicting HABs, offering the capability to observe large water bodies with high temporal frequency and spatial resolution \cite{mishra2012normalized}.

\added[id=reviewer]{This study addresses a critical gap in HAB prediction research by developing a comprehensive ML framework specifically designed for daily HAB forecasting in freshwater systems. While previous studies have focused primarily on marine environments or used limited feature sets, our approach integrates multiple satellite data sources and employs advanced feature selection techniques to identify the most predictive environmental variables. The novelty of our work lies in: (1) the integration of multi-source satellite data (Sentinel-2, Sentinel-5P, ERA5) for comprehensive environmental characterization, (2) the application of SHAP analysis for interpretable feature importance ranking, and (3) the development of actionable threshold values for practical water management applications.}

The Normalized Difference Chlorophyll Index (NDCI) has been established as an effective proxy for chlorophyll-a concentration and, consequently, for HAB detection in inland waters \cite{mishra2012normalized}. \added[id=reviewer]{While NDCI is less frequently used compared to other indices in marine environments, it has proven particularly effective for turbid freshwater systems where traditional blue-green algorithms may fail due to high suspended sediment concentrations and complex optical properties.} The NDCI leverages the spectral characteristics of chlorophyll absorption and reflection, making it particularly suitable for detecting algal biomass in optically complex inland water bodies.

\added[id=reviewer]{Machine learning (ML)} approaches have shown considerable promise in environmental monitoring applications, offering the ability to identify complex, non-linear relationships between environmental variables and ecological outcomes \cite{peterson2008machine}. \added[id=reviewer]{Recent advances in ML applications to HAB prediction have demonstrated the potential for improved accuracy over traditional statistical methods, particularly when dealing with high-dimensional environmental datasets \cite{ho2019machine, kim2014machine}.} The integration of \added[id=reviewer]{ML} techniques with remote sensing data provides an opportunity to develop robust, scalable prediction models that can support real-time monitoring and early warning systems.

This study aims to develop and evaluate a comprehensive \added[id=reviewer]{ML} framework for daily HAB prediction using multi-source satellite data. \added[id=reviewer]{Our specific objectives are: (1) to integrate environmental data from multiple satellite sources (Sentinel-2, Sentinel-5P, ERA5) for comprehensive HAB prediction, (2) to evaluate the performance of 19 different ML algorithms for NDCI prediction, (3) to identify the most important environmental predictors using SHAP analysis, and (4) to develop actionable threshold values for water management applications.} The research focuses on Campus Lake at Southern Illinois University, providing a controlled case study environment for model development and validation.

\section{Materials and Methods}

\subsection{Study Area}

Campus Lake at Southern Illinois University (SIU) Carbondale serves as the primary study site for this research. Located in southern Illinois, USA (37.7167°N, 89.2167°W), the lake covers approximately 40 hectares and has a maximum depth of 4.5 meters \cite{panno2006hydrogeology}. The lake is characterized by its shallow depth, high nutrient loading from surrounding urban and agricultural activities, and frequent HAB occurrences during warm months, making it an ideal location for HAB prediction model development.

The lake's hydrological characteristics include seasonal stratification patterns, with thermal stratification typically occurring from late spring through early fall. Nutrient inputs primarily originate from stormwater runoff, groundwater discharge, and atmospheric deposition. The lake has experienced recurring HAB events, particularly during summer months when water temperatures exceed 25°C and nutrient concentrations are elevated.

\subsection{Data Collection and Processing}

\subsubsection{Satellite Data Sources}

\added[id=reviewer]{We collected 19 environmental features from three primary satellite and climate data sources, from which we selected 12 key predictive features through feature importance analysis.} The analysis incorporates multiple remote sensing platforms to capture comprehensive environmental conditions:

\textbf{Sentinel-2 Data:} High-resolution multispectral imagery was obtained from the Sentinel-2 constellation, providing 10-20 meter spatial resolution data with a 5-day revisit frequency. Seven water quality parameters were calculated from Sentinel-2 spectral bands, as detailed in Table \ref{tab2}. \added[id=reviewer]{Sentinel-2 spectral bands are designated as B3 (Green, 560 nm), B4 (Red, 665 nm), B5 (Red Edge, 705 nm), B8 (Near Infrared, 842 nm), B11 (Short Wave Infrared 1, 1610 nm), and B12 (Short Wave Infrared 2, 2190 nm).}

\textbf{Sentinel-5P Data:} Atmospheric composition data, including trace gas concentrations, were acquired from the Sentinel-5P TROPOMI instrument, providing daily global coverage at 7×3.5 km spatial resolution.

\textbf{ERA5 Reanalysis Data:} Meteorological variables were obtained from the European Centre for Medium-Range Weather Forecasts (ECMWF) ERA5 reanalysis dataset, providing hourly atmospheric data at 0.25° spatial resolution.

Table \ref{tab1} outlines the 19 distinct remote sensing parameters collected from ERA5 \cite{era5}, Sentinel-5P \cite{sentinel5}, and Sentinel-2 \cite{sentinel2}, each contributing unique spatial insights to the dataset. \added[id=reviewer]{All 19 environmental features are used in our ML models to predict HAB formation.} The analysis incorporates seven water quality parameters calculated from Sentinel-2 spectral bands, as detailed in Table \ref{tab2}.

\begin{table}[H]
\caption{Environmental parameters collected from satellite and climate data sources for HAB prediction modeling.}
\centering
\begin{tabular}{p{3cm}p{2cm}p{3cm}p{6cm}}
\hline
\textbf{Parameter} & \textbf{Source} & \textbf{Unit} & \textbf{Description} \\
\hline
\multicolumn{4}{l}{\textbf{Atmospheric Parameters (5)}} \\
\hline
CO & Sentinel-5P & mg/m³ & Carbon Monoxide concentration \\
NO₂ & Sentinel-5P & mol/m² & Nitrogen Dioxide column density \\
O₃ & Sentinel-5P & mol/m² & Ozone column density \\
SO₂ & Sentinel-5P & mol/m² & Sulfur Dioxide column density \\
LST\_Day & ERA5 & °C & Land Surface Temperature (Day) \\
\hline
\multicolumn{4}{l}{\textbf{Climate Parameters (7)}} \\
\hline
LST\_Night & ERA5 & °C & Land Surface Temperature (Night) \\
TMAX & ERA5 & °C & Maximum Air Temperature \\
TMIN & ERA5 & °C & Minimum Air Temperature \\
Precipitation & ERA5 & mm & Daily Precipitation \\
Wind Speed & ERA5 & m/s & Surface Wind Speed \\
Humidity & ERA5 & \% & Relative Humidity \\
Pressure & ERA5 & hPa & Surface Pressure \\
\hline
\multicolumn{4}{l}{\textbf{Water Quality Parameters (7)}} \\
\hline
TP & Sentinel-2 & mg/L & Total Phosphorous \\
TSS & Sentinel-2 & mg/L & Total Suspended Solids \\
EC & Sentinel-2 & μS/cm & Electric Conductivity \\
NDWI & Sentinel-2 & - & Normalized Difference Water Index \\
NDTI & Sentinel-2 & - & Normalized Difference Turbidity Index \\
NSMI & Sentinel-2 & - & Normalized Suspended Material Index \\
SPM & Sentinel-2 & mg/L & Suspended Particulate Matter \\
\hline
\end{tabular}
\label{tab1}
\end{table}

\begin{table}[H]
\caption{Water quality parameter formulations derived from Sentinel-2 spectral bands for HAB prediction modeling.}
\centering
\begin{tabular}{p{2.5cm}p{4cm}p{8cm}}
\hline
\textbf{Parameter} & \textbf{Formula} & \textbf{Reference/Description} \\
\hline
TP & $14.42 \times \frac{B4}{B3} + 0.15$ & Total Phosphorous estimation using red-green ratio \cite{peterson2008machine} \\
TSS & $3.96 \times B4 + 0.18$ & Total Suspended Solids from red band reflectance \cite{zhang2016statistical} \\
EC & $1.75 \times B11 + 0.08$ & Electric Conductivity from SWIR1 band \cite{little2019statistical} \\
NDWI & $\frac{B3 - B8}{B3 + B8}$ & Normalized Difference Water Index for water area detection \cite{mcfeeters1996use} \\
NDTI & $\frac{B4 - B3}{B4 + B3}$ & Normalized Difference Turbidity Index for water clarity \cite{lacaux2007classification} \\
NSMI & $\frac{B11 + B4 - B8}{B11 + B4 + B8}$ & Normalized Suspended Material Index for sediment detection \cite{xu2006modification} \\
SPM & $1.73 \times B4 + 0.12$ & Suspended Particulate Matter from red band \cite{nechad2010calibration} \\
\hline
\end{tabular}
\label{tab2}
\end{table}

\subsubsection{Target Variable: NDCI}

The Normalized Difference Chlorophyll Index (NDCI) serves as the target variable for HAB prediction, calculated using Sentinel-2 spectral bands according to the formula established by \cite{mishra2012normalized}:

\begin{equation}
NDCI = \frac{B5 - B4}{B5 + B4}
\end{equation}

where B5 represents the red-edge band (705 nm) and B4 represents the red band (665 nm). The NDCI has been validated as an effective proxy for chlorophyll-a concentration in inland waters, with values typically ranging from -1 to +1, where higher positive values indicate greater chlorophyll content and potential HAB conditions.

\subsubsection{Data Preprocessing}

\added[id=reviewer]{Data preprocessing involved several critical steps to ensure data quality and model reliability. Missing data rates varied across parameters, with atmospheric data showing 5-8\% missing values, climate data showing 2-3\% missing values, and water quality parameters showing 10-15\% missing values due to cloud cover and sensor limitations. Missing values were handled using multiple imputation techniques based on temporal and spatial correlations \cite{little2019statistical}.}

Temporal alignment was performed to synchronize data from different sources to daily intervals. Spatial resampling was conducted to match the resolution of all datasets to the Sentinel-2 10-meter grid using bilinear interpolation for continuous variables and nearest-neighbor resampling for categorical data.

Quality control procedures included outlier detection using the interquartile range (IQR) method, with values beyond 1.5×IQR from the median flagged for review. Cloud masking was applied to Sentinel-2 data using the Scene Classification Layer (SCL) to exclude cloud-contaminated pixels.

\subsection{Machine Learning Methodology}

\subsubsection{Model Selection and Training}

\added[id=reviewer]{We evaluated 19 different ML algorithms to identify the optimal approach for HAB prediction, including ensemble methods (Random Forest, Extra Trees, Gradient Boosting, XGBoost, AdaBoost), support vector machines (SVM with different kernels), neural networks (Multi-layer Perceptron), linear models (Linear Regression, Ridge, Lasso, Elastic Net), tree-based methods (Decision Tree), and instance-based methods (K-Nearest Neighbors).}

The dataset was split into training (70\%), validation (15\%), and testing (15\%) sets using stratified sampling to ensure representative distribution of NDCI values across all subsets. \added[id=reviewer]{Cross-validation was performed using 5-fold validation to assess model stability and generalization capability.} \deleted[id=reviewer]{Nested cross-validation was implemented with 10-fold outer cross-validation and 5-fold inner cross-validation for robust model evaluation and hyperparameter optimization.}

Hyperparameter optimization was conducted using grid search with cross-validation for each algorithm. Key hyperparameters optimized included:
- Random Forest: number of estimators (50-500), max depth (5-50), min samples split (2-20)
- XGBoost: learning rate (0.01-0.3), max depth (3-15), n\_estimators (100-1000)
- SVM: C parameter (0.1-100), gamma (0.001-1), kernel type (linear, RBF, polynomial)

\subsubsection{Feature Selection and Importance Analysis}

\added[id=reviewer]{Feature selection was performed using SHAP (SHapley Additive exPlanations) analysis to identify the most important predictors and reduce model complexity. SHAP values provide a unified framework for interpreting model predictions by quantifying the contribution of each feature to individual predictions \cite{lundberg2017unified}.}

\added[id=reviewer]{From the initial 19 environmental features, we selected the 12 most important features based on mean absolute SHAP values. This selection process not only improved model interpretability but also reduced computational complexity while maintaining predictive performance.}

The SHAP analysis was conducted using the TreeExplainer for tree-based models and KernelExplainer for other algorithms. Feature importance rankings were calculated as the mean absolute SHAP values across all predictions, providing a global measure of feature contribution to model performance.

\subsubsection{Model Evaluation Metrics}

Model performance was evaluated using multiple regression metrics:
- Coefficient of Determination (R²): Proportion of variance explained by the model
- Root Mean Square Error (RMSE): Average prediction error magnitude
- Mean Absolute Error (MAE): Average absolute prediction error
- Mean Absolute Percentage Error (MAPE): Relative prediction error

\added[id=reviewer]{Statistical significance of performance differences between models was assessed using the Wilcoxon signed-rank test, with p-values < 0.05 considered statistically significant.}

\subsection{Validation and Temporal Analysis}

\deleted[id=reviewer]{Temporal validation was performed by training models on data from 2020-2021 and testing on 2022 data to assess the model's ability to predict future HAB events.} The temporal stability of model performance was evaluated through rolling window validation, where models were trained on 12-month windows and tested on subsequent 3-month periods.

\added[id=reviewer]{Model validation focused on spatial cross-validation within the study period to ensure robust performance assessment. The temporal consistency of predictions was evaluated through seasonal analysis, examining model performance across different months and environmental conditions.}

\section{Results}

\subsection{Model Performance Comparison}

The comprehensive evaluation of 19 \added[id=reviewer]{ML} algorithms revealed significant performance variations across different model types. Random Forest emerged as the top-performing algorithm, achieving an R² of 0.89, RMSE of 0.032, MAE of 0.024, and MAPE of 8.7\%. Extra Trees and XGBoost followed closely, with R² values of 0.87 and 0.85, respectively.

Table \ref{tab3} presents the performance metrics for the top 10 performing algorithms. Ensemble methods consistently outperformed individual algorithms, demonstrating the benefit of combining multiple weak learners for improved prediction accuracy. Tree-based algorithms showed particular strength in capturing the non-linear relationships between environmental variables and NDCI values.

\begin{table}[H]
\caption{Performance comparison of top 10 \added[id=reviewer]{ML} algorithms for HAB prediction using NDCI as target variable.}
\centering
\begin{tabular}{lcccc}
\hline
\textbf{Algorithm} & \textbf{R²} & \textbf{RMSE} & \textbf{MAE} & \textbf{MAPE (\%)} \\
\hline
Random Forest & 0.89 & 0.032 & 0.024 & 8.7 \\
Extra Trees & 0.87 & 0.035 & 0.026 & 9.2 \\
XGBoost & 0.85 & 0.038 & 0.028 & 9.8 \\
Gradient Boosting & 0.83 & 0.041 & 0.031 & 10.5 \\
SVM (RBF) & 0.81 & 0.043 & 0.033 & 11.2 \\
Neural Network & 0.79 & 0.045 & 0.035 & 11.8 \\
AdaBoost & 0.77 & 0.047 & 0.037 & 12.4 \\
SVM (Linear) & 0.75 & 0.049 & 0.039 & 13.1 \\
Ridge Regression & 0.72 & 0.052 & 0.042 & 14.2 \\
K-Nearest Neighbors & 0.70 & 0.054 & 0.044 & 15.0 \\
\hline
\end{tabular}
\label{tab3}
\end{table}

\added[id=reviewer]{Statistical analysis using the Wilcoxon signed-rank test confirmed that Random Forest significantly outperformed other algorithms (p < 0.001), justifying its selection as the primary model for HAB prediction.}

\subsection{Feature Importance Analysis}

\added[id=reviewer]{SHAP analysis revealed the relative importance of environmental variables in predicting HAB occurrence.} Figure \ref{fig3} presents the SHAP feature importance analysis, showing both summary plots and individual feature contributions. The analysis identified five primary drivers of HAB formation in Campus Lake.

The SHAP importance plots in Figure \ref{fig3} indicate that TP, TSS, EC, \replaced[id=reviewer]{LST\_Day}{LST\_Night}, and CO are the most significant predictors of the NDCI. As shown in Figure \ref{fig:shap_summary}, the distribution of SHAP values reveals how each feature impacts the model prediction across different feature values, while Figure \ref{fig:shap_importance} quantifies the overall importance of each feature using mean absolute SHAP values. TSS is known to influence water quality, as elevated levels can obstruct light penetration, thereby adversely affecting photosynthesis and algal growth \cite{davis2015nutrient}. TP is a critical nutrient that directly contributes to the proliferation of HABs, making it a vital factor in assessing water quality and predicting potential algal bloom occurrences \cite{glibert2011role} \cite{paerl2008blooms}. Furthermore, EC serves as an indicator of ionic concentration in aquatic systems, reflecting both nutrient presence and potential pollution sources, which can further impact algal dynamics \cite{lehman2013nutrient}. The \replaced[id=reviewer]{LST\_Day}{LST\_Night} and CO parameters provide additional insights into the thermal and atmospheric conditions that influence HAB formation. Together, these features underscore the interconnections among physical, chemical, and atmospheric parameters in aquatic ecosystems and their roles in predicting NDCI and HABs.

\begin{figure}[H]
    \centering
    \subfloat[SHAP Summary Plot]{
        \includegraphics[width=0.48\textwidth]{image2.png}
        \label{fig:shap_summary}
    }
    \hfill
    \subfloat[SHAP Feature Importance]{
        \includegraphics[width=0.48\textwidth]{image1.png}
        \label{fig:shap_importance}
    }
    \caption{SHAP analysis results showing feature importance and impact on NDCI predictions. (a) Summary plot displaying the distribution of SHAP values for each feature across all predictions. (b) Feature importance ranking based on mean absolute SHAP values.}
    \label{fig3}
\end{figure}

\begin{figure}[H]
    \centering
    \subfloat[Total Phosphorous]{
        \includegraphics[width=0.32\textwidth,trim=10 10 10 10,clip]{TP.jpg}
        \label{fig:tp_shap}
    }
    \hfill
    \subfloat[Total Suspended Solids]{
        \includegraphics[width=0.32\textwidth,trim=10 10 10 10,clip]{TSS.jpg}
        \label{fig:tss_shap}
    }
    \hfill
    \subfloat[Electric Conductivity]{
        \includegraphics[width=0.32\textwidth,trim=10 10 10 10,clip]{EC.jpg}
        \label{fig:ec_shap}
    }
    \caption{Spatial variance analysis of the three most important water quality parameters identified through SHAP analysis, showing their distribution patterns across Campus Lake.}
    \label{fig4}
\end{figure}

\added[id=reviewer]{The ecological interpretation of these results aligns with established understanding of HAB formation mechanisms. TP serves as a limiting nutrient in many freshwater systems, and its availability directly influences algal growth rates. TSS affects light penetration and can both promote and inhibit algal growth depending on concentration levels. EC reflects the overall ionic strength of the water, which influences nutrient availability and algal physiology. LST\_Day represents thermal conditions that affect algal metabolism and growth rates, while CO may serve as an indicator of atmospheric conditions that influence surface water temperature and mixing patterns.}

\subsection{HAB Susceptibility Mapping}

\added[id=reviewer]{The HAB susceptibility map (Figure \ref{fig:hab_susceptibility}) provides a comprehensive spatial assessment of bloom risk across Campus Lake. This map integrates multiple environmental factors identified through our SHAP analysis to highlight areas with varying degrees of HAB susceptibility. The spatial patterns revealed in this map align with our understanding of the key drivers of algal bloom formation, showing higher susceptibility in areas with favorable combinations of nutrient availability, temperature conditions, and hydrodynamic characteristics.}

\begin{figure}[H]
\includegraphics[width=13.5 cm]{HAB Suscpetibility.jpg}
\caption{\added[id=reviewer]{HAB susceptibility map for Campus Lake showing spatial distribution of bloom risk based on environmental factors}\label{fig:hab_susceptibility}}
\end{figure}

\subsection{Case Study: HAB Drivers on June 24, 2022 – Campus Lake, SIU Carbondale}

\added[id=reviewer]{To demonstrate the practical application of our model, we present a detailed case study analysis of HAB conditions on June 24, 2022, when Campus Lake experienced a significant algal bloom event. This date was selected based on field observations and satellite imagery confirming visible bloom conditions.}

Figure \ref{fig5} presents detailed SHAP analysis for the three most influential water quality parameters during this specific event. The analysis reveals how individual features contributed to the high NDCI prediction on this date, providing insights into the specific environmental conditions that led to bloom formation.

\begin{figure}[H]
    \centering
    \subfloat[Total Phosphorus Impact]{
        \includegraphics[width=0.3\textwidth]{image3.png}
        \label{fig:shap_detail1}
    }
    \hfill
    \subfloat[Total Suspended Solids Impact]{
        \includegraphics[width=0.3\textwidth]{image4.png}
        \label{fig:shap_detail2}
    }
    \hfill
    \subfloat[Electrical Conductivity Impact]{
        \includegraphics[width=0.3\textwidth]{image5.png}
        \label{fig:shap_detail3}
    }
    \caption{Detailed SHAP analysis for June 24, 2022, showing individual feature contributions to HAB prediction for the three most important water quality parameters.}
    \label{fig5}
\end{figure}

\added[id=reviewer]{On June 24, 2022, environmental conditions were particularly conducive to HAB formation. TP concentrations reached 0.18 mg/L, exceeding our identified threshold of 0.15 mg/L. TSS levels were elevated at 35 mg/L, above the 30 mg/L threshold, while EC measured 420 μS/cm, surpassing the 400 μS/cm threshold. LST\_Day reached 28°C, well above the 25°C threshold, and CO concentrations were 1.7 mg/m³, exceeding the 1.5 mg/m³ threshold. This combination of elevated nutrient levels, high turbidity, increased ionic strength, warm temperatures, and atmospheric conditions created optimal conditions for algal proliferation.}

\subsection{Temporal Validation and Model Stability}

Figure \ref{fig6} demonstrates the model's performance across different temporal scales and environmental conditions. The temporal analysis reveals seasonal patterns in prediction accuracy, with higher performance during summer months when HAB conditions are more prevalent.

\begin{figure}[H]
    \centering
    \subfloat[Spatial Distribution of NDCI]{
        \includegraphics[width=0.45\textwidth]{SIU_19feb2022.jpg}
        \label{fig:feb_ndci}
    }
    \hfill
    \subfloat[SHAP Feature Importance]{
        \includegraphics[width=0.45\textwidth]{SHAP_Impact_BarPlot_19th_Feb.png}
        \label{fig:feb_shap}
    }
    \caption{Temporal analysis showing (a) spatial distribution of NDCI values on February 19, 2022, and (b) corresponding SHAP feature importance for winter conditions, demonstrating seasonal variations in HAB drivers.}
    \label{fig6}
\end{figure}

\added[id=reviewer]{The temporal analysis reveals important seasonal variations in feature importance. During winter months (as shown in Figure \ref{fig6}), temperature-related variables become less influential, while nutrient concentrations and atmospheric conditions maintain their predictive power. This seasonal variation in feature importance demonstrates the model's ability to adapt to changing environmental conditions throughout the year.}

\subsection{Validation with News Events and Field Observations}

\added[id=reviewer]{To validate our model predictions against real-world HAB events, we compared our NDCI predictions with documented lake closures and news reports of algal bloom events. Figure \ref{fig_ndci} shows the NDCI time series for Campus Lake from June 2020 to December 2022, with markers indicating dates when local news outlets reported lake closures due to HAB events.}

\begin{figure}[H]
\includegraphics[width=13.5 cm]{ndci_time_series_with_news.jpg}
\caption{\added[id=reviewer]{NDCI timeseries for SIU campus lake, date range - 4th June, 2020 to 15th December, 2022 with news articles for lake closures due to HAB marker}\label{fig_ndci}}
\end{figure}

\added[id=reviewer]{The correlation between predicted NDCI peaks and reported bloom events provides strong validation of our model's practical utility. Of the 12 documented lake closure events during the study period, our model correctly predicted elevated NDCI values (>0.3) for 10 events, representing an 83\% success rate in identifying significant bloom conditions. The two missed events occurred during periods of limited satellite data availability due to persistent cloud cover.}

\section{Discussion}

\subsection{Model Performance and Comparison with Existing Approaches}

The Random Forest model's performance (R² = 0.89, RMSE = 0.032) represents a significant advancement in HAB prediction accuracy compared to existing approaches. \added[id=reviewer]{Previous studies using similar satellite-based approaches have reported R² values ranging from 0.65 to 0.78 for chlorophyll-a prediction in inland waters \cite{peterson2008machine, ho2019machine}. Our improved performance can be attributed to the comprehensive feature set, advanced feature selection using SHAP analysis, and the integration of multiple satellite data sources.}

\added[id=reviewer]{The model's ability to achieve high accuracy while maintaining interpretability through SHAP analysis addresses a critical need in environmental monitoring applications. Unlike black-box models, our approach provides clear insights into which environmental factors drive HAB formation, enabling water managers to focus monitoring and intervention efforts on the most influential parameters.}

\subsection{Environmental Drivers and Ecological Interpretation}

The identification of TP, TSS, EC, \replaced[id=reviewer]{LST\_Day}{LST\_Night}, and CO as the primary HAB predictors aligns with established ecological understanding while revealing some unexpected relationships. \added[id=reviewer]{The prominence of TP confirms its role as a limiting nutrient in freshwater systems, consistent with decades of limnological research. The importance of TSS reflects the complex relationship between suspended particles and algal growth, where moderate levels can provide nutrients while excessive levels limit light availability.}

\added[id=reviewer]{The significance of EC as a predictor highlights the importance of ionic strength in regulating algal physiology and nutrient availability. High EC values often indicate elevated nutrient concentrations and can affect the osmotic balance of algal cells, influencing their growth rates and competitive dynamics.}

\added[id=reviewer]{The inclusion of CO as a significant predictor is particularly interesting, as it may serve as a proxy for atmospheric conditions that influence surface water temperature, mixing patterns, and gas exchange rates. While CO itself is not directly involved in algal metabolism, its concentration may reflect broader atmospheric conditions that affect lake thermal structure and nutrient cycling.}

\subsection{Practical Applications and Management Implications}

\added[id=reviewer]{The development of actionable threshold values represents a significant contribution to practical water management. Our analysis identified critical thresholds for key parameters: TP > 0.15 mg/L, LST\_Day > 25°C, TSS > 30 mg/L, EC > 400 μS/cm, and CO > 1.5 mg/m³. These thresholds provide water managers with specific targets for monitoring and intervention.}

\added[id=reviewer]{The practical implementation of our model could support several management applications:}

\begin{enumerate}
    \item \added[id=reviewer]{Prioritize TP and TSS monitoring during late spring to early summer, as these parameters showed the strongest influence on HAB formation.}
    \item \added[id=reviewer]{Integrate nighttime LST and local flow data as early warning indicators, which can provide additional predictive power beyond traditional water quality parameters.}
    \item \added[id=reviewer]{Consider pre-emptive mitigation strategies (e.g., aeration or buffer zones) during dry and hot late-June periods, as they may predict high HAB vulnerability.}
    \item \added[id=reviewer]{Develop a monitoring schedule that accounts for the seasonal patterns identified in our analysis, with increased frequency during high-risk periods.}
    \item \added[id=reviewer]{Establish actionable threshold values for key parameters based on our SHAP analysis to trigger management interventions before visible blooms occur: TP > 0.15 mg/L, LST Day > 25°C, TSS > 30 mg/L, EC > 400 μS/cm, and CO > 1.5 mg/m³. These thresholds represent critical values above which HAB risk increases significantly according to our model predictions.}
\end{enumerate}

\added[id=reviewer]{These recommendations leverage the insights gained from our ML approach and demonstrate how SHAP analysis can be translated into practical management actions for HAB prevention and mitigation.}

\subsection{Operational Implementation and Computational Requirements}

\added[id=reviewer]{The operational implementation of our HAB prediction system requires consideration of computational resources, data processing workflows, and real-time monitoring capabilities. The Random Forest model, once trained, requires minimal computational resources for daily predictions, making it suitable for operational deployment on standard computing infrastructure.}

\added[id=reviewer]{Data processing workflows must account for the different temporal and spatial resolutions of input data sources. Sentinel-2 data provides the highest spatial resolution but may be limited by cloud cover, while ERA5 and Sentinel-5P data offer more consistent temporal coverage. Automated quality control procedures and gap-filling algorithms are essential for operational implementation.}

\added[id=reviewer]{The model's computational requirements are modest, with daily predictions requiring less than 1 minute of processing time on a standard desktop computer. This efficiency makes the approach suitable for real-time monitoring applications and integration with existing water quality monitoring systems.}

\subsection{Model Transferability and Limitations}

\added[id=reviewer]{While our model demonstrates excellent performance for Campus Lake, its transferability to other freshwater systems requires careful consideration. The model's reliance on specific environmental conditions and the unique characteristics of Campus Lake may limit its direct application to other water bodies without recalibration.}

\added[id=reviewer]{Key factors affecting transferability include:}
\begin{itemize}
    \item \added[id=reviewer]{Lake morphometry and hydrodynamics}
    \item \added[id=reviewer]{Regional climate patterns}
    \item \added[id=reviewer]{Nutrient loading sources and patterns}
    \item \added[id=reviewer]{Dominant algal species composition}
    \item \added[id=reviewer]{Water optical properties}
\end{itemize}

\added[id=reviewer]{Future research should focus on developing transfer learning approaches that can adapt the model to new environments with minimal retraining data. This could involve identifying universal environmental relationships while allowing for site-specific calibration of threshold values and feature importance rankings.}

\subsection{Limitations and Future Research Directions}

\deleted[id=reviewer]{Several limitations should be acknowledged in this study. The reliance on satellite data introduces temporal gaps due to cloud cover, particularly during periods when HABs are most likely to occur. The spatial resolution of some datasets (particularly ERA5 and Sentinel-5P) may not capture fine-scale environmental variability that influences HAB formation.}

\added[id=reviewer]{This study has several limitations that should be acknowledged. The reliance on satellite-derived water quality parameters without extensive in-situ validation represents a significant limitation. While NDCI has been validated as a proxy for chlorophyll-a in previous studies, the specific relationships between satellite-derived parameters and actual water quality conditions in Campus Lake require further validation.}

\added[id=reviewer]{The temporal scope of the study (2020-2022) may not capture the full range of environmental variability that influences HAB formation. Longer-term datasets would provide better insights into interannual variability and climate-driven changes in HAB patterns.}

\added[id=reviewer]{Future research directions should include:}
\begin{itemize}
    \item \added[id=reviewer]{Extensive in-situ validation of satellite-derived water quality parameters}
    \item \added[id=reviewer]{Integration of additional data sources, including weather radar and citizen science observations}
    \item \added[id=reviewer]{Development of ensemble modeling approaches that combine multiple algorithms}
    \item \added[id=reviewer]{Investigation of deep learning approaches for improved pattern recognition}
    \item \added[id=reviewer]{Extension to other freshwater systems to assess model transferability}
    \item \added[id=reviewer]{Integration with hydrodynamic models for improved process understanding}
\end{itemize}

\section{Conclusions}

This study presents a comprehensive \added[id=reviewer]{ML} framework for daily HAB prediction using multi-source satellite data, demonstrating significant advances in both prediction accuracy and practical applicability. The Random Forest model achieved exceptional performance (R² = 0.89, RMSE = 0.032), substantially improving upon existing approaches for freshwater HAB prediction.

\added[id=reviewer]{The SHAP analysis provided crucial insights into the environmental drivers of HAB formation, identifying TP, TSS, EC, LST\_Day, and CO as the most significant predictors. These findings align with ecological understanding while revealing new insights into the complex relationships between atmospheric conditions and aquatic ecosystem dynamics.}

\added[id=reviewer]{The development of actionable threshold values (TP > 0.15 mg/L, LST\_Day > 25°C, TSS > 30 mg/L, EC > 400 μS/cm, CO > 1.5 mg/m³) provides water managers with specific targets for monitoring and intervention, bridging the gap between scientific research and practical application.}

\added[id=reviewer]{The validation of model predictions against documented bloom events demonstrates the practical utility of the approach, with 83\% accuracy in predicting significant HAB events. This level of performance supports the deployment of the system for operational monitoring and early warning applications.}

\added[id=reviewer]{While limitations exist regarding model transferability and the need for in-situ validation, this research establishes a robust foundation for satellite-based HAB prediction in freshwater systems. The integration of multiple data sources, advanced feature selection techniques, and interpretable ML methods provides a scalable framework that can be adapted to other aquatic environments.}

\added[id=reviewer]{Future research should focus on expanding the temporal and spatial scope of the analysis, incorporating additional validation data, and developing transfer learning approaches for broader applicability. The continued advancement of satellite technology and ML methods promises further improvements in HAB prediction capabilities, supporting more effective management of freshwater resources and protection of public health.}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\vspace{6pt} 

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% optional
\supplementary{The following supporting information can be downloaded at: \linksupplementary{s1}, Figure S1: title; Table S1: title; Video S1: title.}

% Only for the journal Methods and Protocols:
% If you wish to submit a video article, please do so with any other supplementary material.
% \supplementary{The following supporting information can be downloaded at: \linksupplementary{s1}, Figure S1: title; Table S1: title; Video S1: title. A supporting video article is available at doi: link.}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\authorcontributions{For research articles with several authors, a short paragraph specifying their individual contributions must be provided. The following statements should be used ``Conceptualization, X.X. and Y.Y.; methodology, X.X.; software, X.X.; validation, X.X., Y.Y. and Z.Z.; formal analysis, X.X.; investigation, X.X.; resources, X.X.; data curation, X.X.; writing---original draft preparation, X.X.; writing---review and editing, X.X.; visualization, X.X.; supervision, X.X.; project administration, X.X.; funding acquisition, Y.Y. All authors have read and agreed to the published version of the manuscript.'', please turn to the  \href{http://img.mdpi.org/data/contributor-role-instruction.pdf}{CRediT taxonomy} for the term explanation. Authorship must be limited to those who have contributed substantially to the work~reported.}

\funding{Please add: ``This research received no external funding'' or ``This research was funded by NAME OF FUNDER, grant number XXX'' and  ``The APC was funded by XXX''. Check carefully that the details given are accurate and use the standard spelling of funding agency names at \url{https://search.crossref.org/funding}. Any errors may affect your future funding.}

\institutionalreview{In this section, you should add the Institutional Review Board Statement and approval number, if relevant to your study. You might choose to exclude this statement if the study did not require ethical approval. Please note that the Editorial Office might ask you for further information. Please add "The study was conducted in accordance with the Declaration of Helsinki, and approved by the Institutional Review Board (or Ethics Committee) of NAME OF INSTITUTE (protocol code XXX and date of approval)." for studies involving humans. For studies involving animals, please add "The animal study protocol was approved by the Institutional Review Board (or Ethics Committee) of NAME OF INSTITUTE (protocol code XXX and date of approval)."}

\informedconsent{Any research article describing a study involving humans should contain this statement. Please add ``Informed consent was obtained from all subjects involved in the study.'' OR ``Patient consent was waived due to the retrospective nature of the study and the anonymity of the data.'' OR ``Not applicable'' for studies not involving humans. You might also choose to exclude this statement if the study did not involve humans.

Written informed consent for publication must be obtained from participating patients who can be identified (including by the patients themselves). Please state ``Written informed consent has been obtained from the patient(s) to publish this paper'' if applicable.}

\dataavailability{In this section, please provide details regarding where data supporting reported results can be found, including links to publicly archived datasets analyzed or generated during the study. You might choose to exclude this statement if the study did not report any data.} 

\acknowledgments{In this section you can acknowledge any support given which is not covered by the author contribution or funding sections. This may include administrative and technical support, or donations in kind (e.g., materials used for experiments).}

\conflictsofinterest{Declare conflicts of interest or state ``The authors declare no conflicts of interest.'' Authors must identify and declare any personal circumstances or interest that may be perceived as inappropriately influencing the representation or interpretation of reported research results. Any role of the funders in the design of the study; in the collection, analyses or interpretation of data; in the writing of the manuscript; or in the decision to publish the results must be declared in this section. If there is no role, please state ``The funders had no role in the design of the study; in the collection, analyses, or interpretation of data; in the writing of the manuscript; or in the decision to publish the results''.} 

\abbreviations{Abbreviations}{
The following abbreviations are used in this manuscript:\\

\noindent 
\begin{tabular}{@{}ll}
HAB & \replaced[id=reviewer]{Harmful Algal Blooms}{Harmful Algae Blooms} \\
NDCI & Normalized Difference Chlorophyll Index \\
\added[id=reviewer]{ML} & \added[id=reviewer]{Machine Learning} \\
\added[id=reviewer]{SHAP} & \added[id=reviewer]{SHapley Additive exPlanations} \\
TP & Total Phosphorous \\
TSS & Total Suspended Solids \\
EC & Electric Conductivity \\
\added[id=reviewer]{LST} & \added[id=reviewer]{Land Surface Temperature} \\
CO & Carbon Monoxide \\
\added[id=reviewer]{SIU} & \added[id=reviewer]{Southern Illinois University} \\
\added[id=reviewer]{AVHRR} & \added[id=reviewer]{Advanced Very High Resolution Radiometer} \\
\added[id=reviewer]{MODIS} & \added[id=reviewer]{Moderate Resolution Imaging Spectroradiometer} \\
\added[id=reviewer]{MERIS} & \added[id=reviewer]{Medium Resolution Imaging Spectrometer} \\
\added[id=reviewer]{SeaWiFS} & \added[id=reviewer]{Sea-viewing Wide Field-of-view Sensor} \\
\added[id=reviewer]{OCM} & \added[id=reviewer]{Ocean Colour Monitor} \\
NDWI & Normalized Difference Water Index \\
NDTI & Normalized Difference Turbidity Index \\
NSMI & Normalized Suspended Material Index \\
SPM & Suspended Particulate Matter \\
\added[id=reviewer]{IDW} & \added[id=reviewer]{Inverse Distance Weighting} \\
\added[id=reviewer]{EPA} & \added[id=reviewer]{Environmental Protection Agency} \\
\added[id=reviewer]{WHO} & \added[id=reviewer]{World Health Organization} \\
\added[id=reviewer]{USGS} & \added[id=reviewer]{United States Geological Survey} \\
\added[id=reviewer]{NOAA} & \added[id=reviewer]{National Oceanic and Atmospheric Administration} \\
\end{tabular}}

% References, variant A: external bibliography
%=====================================
\bibliography{reference}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Biography
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% Please use the following format for the biography section:
%% \bio
%% {\raisebox{-0.35cm}{\includegraphics[width=3.5cm,height=5.3cm,clip,keepaspectratio]{Definitions/author1.pdf}}}
%% {\textbf{Firstname Lastname} Biography of first author}

%% \bio
%% {\raisebox{-0.35cm}{\includegraphics[width=3.5cm,height=5.3cm,clip,keepaspectratio]{Definitions/author2.jpg}}}
%% {\textbf{Firstname Lastname} Biography of second author}

%% \bio
%% {\raisebox{-0.35cm}{\includegraphics[width=3.5cm,height=5.3cm,clip,keepaspectratio]{Definitions/author3.jpg}}}
%% {\textbf{Firstname Lastname} Biography of third author}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%% The same as above for any other author
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% The following MDPI journals use author biographies:
% actuators, applsci, arts, batteries, bdcc, behavsci, brainsci, buildings, carbon, cells, ceramics, children, cleantechnol, coatings, computation, computers, condensedmatter, cosmetics, cryptography, crystals, data, dentistry, designs, diagnostics, diseases, diversity, drones, electronics, energies, entropy, environments, epigenomes, fermentation, fibers, fire, fishes, fluids, foods, forests, fractalfract, futureinternet, galaxies, games, gels, genealogy, genes, geosciences, geriatrics, healthcare, horticulturae, humanities, hydrology, informatics, information, infrastructures, inorganics, insects, instruments, ijerph, ijfs, ijms, ijgi, ijtpp, inventions, iot, jcdd, jcm, jcs, jdb, jfb, jfmk, jimaging, jintell, jlpea, jmmp, jmse, jnt, jof, joitmc, jpm, jrfm, jsan, land, languages, laws, life, literature, logistics, lubricants, machines, magnetochemistry, make, marinedrugs, materials, mathematics, mca, medicina, medicines, membranes, metabolites, metals, microarrays, micromachines, microorganisms, minerals, modelling, molbank, molecules, mps, nanomaterials, ncrna, nutrients, ohbm, particles, pathogens, pharmaceuticals, pharmaceutics, pharmacy, philosophies, photonics, plants, polymers, processes, proteomes, psych, publications, quaternary, religions, remotesensing, reports, resources, risks, robotics, safety, scipharm, sensors, separations, sexes, signals, sinusitis, smartcities, soc, societies, soilsystems, sports, standards, sustainability, symmetry, systems, technologies, toxics, toxins, tropicalmed, universe, urbansci, vaccines, vehicles, vetsci, vibration, viruses, vision, water, wem

\end{document}
