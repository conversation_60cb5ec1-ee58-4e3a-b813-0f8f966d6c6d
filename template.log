This is pdfTeX, Version 3.141592653-2.6-1.40.22 (TeX Live 2022/dev/Debian) (preloaded format=pdflatex 2025.4.3)  1 AUG 2025 14:57
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**template.tex
(./template.tex
LaTeX2e <2021-11-15> patch level 1
L3 programming layer <2022-01-21> (./Definitions/mdpi.cls
Document Class: Definitions/mdpi 12/09/2024 MDPI paper class
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2021/10/04 v1.4n Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2021/10/04 v1.4n Standard LaTeX file (size option)
)
\c@part=\count185
\c@section=\count186
\c@subsection=\count187
\c@subsubsection=\count188
\c@paragraph=\count189
\c@subparagraph=\count190
\c@figure=\count191
\c@table=\count192
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
)
(/usr/share/texlive/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(/usr/share/texlive/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks16
\inpenc@posthook=\toks17
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/calc.sty
Package: calc 2017/05/25 v4.3 Infix arithmetic (KKT,FJ)
\calc@Acount=\count193
\calc@Bcount=\count194
\calc@Adimen=\dimen139
\calc@Bdimen=\dimen140
\calc@Askip=\skip49
\calc@Bskip=\skip50
LaTeX Info: Redefining \setlength on input line 80.
LaTeX Info: Redefining \addtolength on input line 81.
\calc@Ccount=\count195
\calc@Cskip=\skip51
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 1995/11/23 v1.03 Indent first paragraph (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2021/01/28 v4.0.1 Extensive control of page headers and foote
rs
\f@nch@headwidth=\skip52
\f@nch@O@elh=\skip53
\f@nch@O@erh=\skip54
\f@nch@O@olh=\skip55
\f@nch@O@orh=\skip56
\f@nch@O@elf=\skip57
\f@nch@O@erf=\skip58
\f@nch@O@olf=\skip59
\f@nch@O@orf=\skip60
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks18
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2021/03/04 v1.4d Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2020/10/05 v1.2a Graphics/color driver for pdftex
))
\Gin@req@height=\dimen141
\Gin@req@width=\dimen142
)
(/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf.sty
Package: epstopdf 2020-01-24 v2.11 Conversion with epstopdf on the fly (HO)

(/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)

(/usr/share/texlive/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
))
(/usr/share/texlive/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)

(/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
))
(/usr/share/texlive/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2020/03/06 v1.0d TeX engine tests
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
(/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPE
G,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
)))
(/usr/share/texlive/texmf-dist/tex/latex/lastpage/lastpage.sty
Package: lastpage 2021/09/03 v1.2n Refers to last page's name (HMM; JPG)
)
(/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2020/11/24 v1.1c Standard LaTeX ifthen package (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count196
\float@exts=\toks19
\float@box=\box50
\@float@everytoks=\toks20
\@floatcapt=\box51
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2021/10/15 v2.17l AMS math features
\@mathmargin=\skip61

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen143
))
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen144
)
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2021/08/26 v2.02 operator names
)
\inf@bad=\count197
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count198
\leftroot@=\count199
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count266
\DOTSCASE@=\count267
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box52
\strutbox@=\box53
\big@size=\dimen145
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count268
\c@MaxMatrixCols=\count269
\dotsspace@=\muskip16
\c@parentequation=\count270
\dspbrk@lvl=\count271
\tag@help=\toks22
\row@=\count272
\column@=\count273
\maxfields@=\count274
\andhelp@=\toks23
\eqnshift@=\dimen146
\alignsep@=\dimen147
\tagshift@=\dimen148
\tagwidth@=\dimen149
\totwidth@=\dimen150
\lineht@=\dimen151
\@envbody=\toks24
\multlinegap=\skip62
\multlinetaggap=\skip63
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2938.
LaTeX Info: Redefining \] on input line 2939.
)
(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/share/texlive/texmf-dist/tex/latex/lineno/lineno.sty
Package: lineno 2005/11/02 line numbers on paragraphs v4.41
\linenopenalty=\count275
\output=\toks26
\linenoprevgraf=\count276
\linenumbersep=\dimen152
\linenumberwidth=\dimen153
\c@linenumber=\count277
\c@pagewiselinenumber=\count278
\c@LN@truepage=\count279
\c@internallinenumber=\count280
\c@internallinenumbers=\count281
\quotelinenumbersep=\dimen154
\bframerule=\dimen155
\bframesep=\dimen156
\bframebox=\box54
LaTeX Info: Redefining \\ on input line 3056.
)
(/usr/share/texlive/texmf-dist/tex/latex/setspace/setspace.sty
Package: setspace 2011/12/19 v6.7a set line spacing
)
(/usr/share/texlive/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip64
\enit@outerparindent=\dimen157
\enit@toks=\toks27
\enit@inbox=\box55
\enit@count@id=\count282
\enitdp@description=\count283
)
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/mathpazo.sty
Package: mathpazo 2020/03/25 PSNFSS-v9.3 Palatino w/ Pazo Math (D.Puga, WaS) 
\symupright=\mathgroup6
)
(/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen158
\lightrulewidth=\dimen159
\cmidrulewidth=\dimen160
\belowrulesep=\dimen161
\belowbottomsep=\dimen162
\aboverulesep=\dimen163
\abovetopsep=\dimen164
\cmidrulesep=\dimen165
\cmidrulekern=\dimen166
\defaultaddspace=\dimen167
\@cmidla=\count284
\@cmidlb=\count285
\@aboverulesep=\dimen168
\@belowrulesep=\dimen169
\@thisruleclass=\count286
\@lastruleclass=\count287
\@thisrulewidth=\dimen170
)
(/usr/share/texlive/texmf-dist/tex/latex/titlesec/titlesec.sty
Package: titlesec 2021/07/05 v2.14 Sectioning titles
\ttl@box=\box56
\beforetitleunit=\skip65
\aftertitleunit=\skip66
\ttl@plus=\dimen171
\ttl@minus=\dimen172
\ttl@toksa=\toks28
\titlewidth=\dimen173
\titlewidthlast=\dimen174
\titlewidthfirst=\dimen175
)
(/usr/share/texlive/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count288
)
(/usr/share/texlive/texmf-dist/tex/latex/tabto-ltx/tabto.sty
Package: tabto 2018/12/28  v 1.4  Another tabbing mechanism
\CurrentLineWidth=\dimen176
\TabPrevPos=\dimen177
)
(/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2021/10/31 v2.13 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 227.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1352.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1356.
Package xcolor Info: Model `RGB' extended on input line 1368.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1370.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1374.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1375.
)
(/usr/share/texlive/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2020/01/04 v1.0e Color table columns (DPC)

(/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2021/10/04 v2.5f Tabular extension package (FMi)
\col@sep=\dimen178
\ar@mcellbox=\box57
\extrarowheight=\dimen179
\NC@list=\toks29
\extratabsurround=\skip67
\backup@length=\skip68
\ar@cellbox=\box58
)
\everycr=\toks30
\minrowclearance=\skip69
)
(/usr/share/texlive/texmf-dist/tex/generic/soul/soul.sty
Package: soul 2003/11/17 v2.4 letterspacing/underlining (mf)
\SOUL@word=\toks31
\SOUL@lasttoken=\toks32
\SOUL@cmds=\toks33
\SOUL@buffer=\toks34
\SOUL@token=\toks35
\SOUL@spaceskip=\skip70
\SOUL@ttwidth=\dimen180
\SOUL@uldp=\dimen181
\SOUL@ulht=\dimen182
)
(/usr/share/texlive/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip71
\multirow@cntb=\count289
\multirow@dima=\skip72
\bigstrutjot=\dimen183
)
(/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2021/12/10 v3.0b Micro-typographical refinements (RS)
\MT@toks=\toks36
\MT@count=\count290
\MT@tempbox=\box59
LaTeX Info: Redefining \leftprotrusion on input line 1010.
LaTeX Info: Redefining \rightprotrusion on input line 1018.
LaTeX Info: Redefining \textls on input line 1173.
\MT@outer@kern=\dimen184
LaTeX Info: Redefining \textmicrotypecontext on input line 1759.
\MT@listname@count=\count291

(/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype-pdftex.def
File: microtype-pdftex.def 2021/12/10 v3.0b Definitions specific to pdftex (RS)

LaTeX Info: Redefining \lsstyle on input line 897.
LaTeX Info: Redefining \lslig on input line 897.
\MT@outer@space=\skip73
)
Package microtype Info: Loading configuration file microtype.cfg.

(/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2021/12/10 v3.0b microtype main configuration file (RS)
))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks37
\pgfutil@tempdima=\dimen185
\pgfutil@tempdimb=\dimen186

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.t
ex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box60
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2021/05/15 v3.1.9a (3.1.9a)
))
Package: pgf 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks38
\pgfkeys@temptoks=\toks39

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.t
ex
\pgfkeys@tmptoks=\toks40
))
\pgf@x=\dimen187
\pgf@y=\dimen188
\pgf@xa=\dimen189
\pgf@ya=\dimen190
\pgf@xb=\dimen191
\pgf@yb=\dimen192
\pgf@xc=\dimen193
\pgf@yc=\dimen194
\pgf@xd=\dimen195
\pgf@yd=\dimen196
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count292
\c@pgf@countb=\count293
\c@pgf@countc=\count294
\c@pgf@countd=\count295
\t@pgf@toka=\toks41
\t@pgf@tokb=\toks42
\t@pgf@tokc=\toks43
\pgf@sys@id@count=\count296
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2021/05/15 v3.1.9a (3.1.9a)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.de
f
File: pgfsys-common-pdf.def 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.
tex
File: pgfsyssoftpath.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfsyssoftpath@smallbuffer@items=\count297
\pgfsyssoftpath@bigbuffer@items=\count298
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.
tex
File: pgfsysprotocol.code.tex 2021/05/15 v3.1.9a (3.1.9a)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen197
\pgfmath@count=\count299
\pgfmath@box=\box61
\pgfmath@toks=\toks44
\pgfmath@stack@operand=\toks45
\pgfmath@stack@operation=\toks46
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonomet
ric.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerari
thmetics.code.tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count300
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.te
x
File: pgfcorepoints.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@picminx=\dimen198
\pgf@picmaxx=\dimen199
\pgf@picminy=\dimen256
\pgf@picmaxy=\dimen257
\pgf@pathminx=\dimen258
\pgf@pathmaxx=\dimen259
\pgf@pathminy=\dimen260
\pgf@pathmaxy=\dimen261
\pgf@xx=\dimen262
\pgf@xy=\dimen263
\pgf@yx=\dimen264
\pgf@yy=\dimen265
\pgf@zx=\dimen266
\pgf@zy=\dimen267
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.
code.tex
File: pgfcorepathconstruct.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@path@lastx=\dimen268
\pgf@path@lasty=\dimen269
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code
.tex
File: pgfcorepathusage.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@shorten@end@additional=\dimen270
\pgf@shorten@start@additional=\dimen271
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.te
x
File: pgfcorescopes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfpic=\box62
\pgf@hbox=\box63
\pgf@layerbox@main=\box64
\pgf@picture@serial@count=\count301
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.c
ode.tex
File: pgfcoregraphicstate.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgflinewidth=\dimen272
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformation
s.code.tex
File: pgfcoretransformations.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@pt@x=\dimen273
\pgf@pt@y=\dimen274
\pgf@pt@temp=\dimen275
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.t
ex
File: pgfcoreobjects.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing
.code.tex
File: pgfcorepathprocessing.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.te
x
File: pgfcorearrows.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfarrowsep=\dimen276
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@max=\dimen277
\pgf@sys@shading@range@num=\count302
\pgf@shadingcount=\count303
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.
tex
File: pgfcoreexternal.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfexternal@startupbox=\box65
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.te
x
File: pgfcorelayers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.c
ode.tex
File: pgfcoretransparency.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.
tex
File: pgfcorepatterns.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfnodeparttextbox=\box66
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65
.sty
Package: pgfcomp-version-0-65 2021/05/15 v3.1.9a (3.1.9a)
\pgf@nodesepstart=\dimen278
\pgf@nodesepend=\dimen279
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18
.sty
Package: pgfcomp-version-1-18 2021/05/15 v3.1.9a (3.1.9a)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen280
\pgffor@skip=\dimen281
\pgffor@stack=\toks47
\pgffor@toks=\toks48
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2021/05/15 v3.1.9a (3.1.9a)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers
.code.tex
File: pgflibraryplothandlers.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgf@plot@mark@count=\count304
\pgfplotmarksize=\dimen282
)
\tikz@lastx=\dimen283
\tikz@lasty=\dimen284
\tikz@lastxsaved=\dimen285
\tikz@lastysaved=\dimen286
\tikz@lastmovetox=\dimen287
\tikz@lastmovetoy=\dimen288
\tikzleveldistance=\dimen289
\tikzsiblingdistance=\dimen290
\tikz@figbox=\box67
\tikz@figbox@bg=\box68
\tikz@tempbox=\box69
\tikz@tempbox@bg=\box70
\tikztreelevel=\count305
\tikznumberofchildren=\count306
\tikznumberofcurrentchild=\count307
\tikz@fig@count=\count308

(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2021/05/15 v3.1.9a (3.1.9a)
\pgfmatrixcurrentrow=\count309
\pgfmatrixcurrentcolumn=\count310
\pgf@matrix@numberofcolumns=\count311
)
\tikz@expandcount=\count312

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2021/05/15 v3.1.9a (3.1.9a)
)))
(/usr/share/texlive/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/changepage/changepage.sty
Package: changepage 2009/10/20 v1.0c check page and change page layout
\c@cp@cntr=\count313
\cp@tempcnt=\count314
)
(/usr/share/texlive/texmf-dist/tex/latex/frankenstein/attrib.sty
Package: attrib 2001/08/31 v1.3 attribution of block elements (Frankenstein's h
at)

(/usr/share/texlive/texmf-dist/tex/latex/frankenstein/moredefs.sty
Package: moredefs 2001/08/31 v1.8 more defining commands (Frankenstein's brain)

\sc@toks@a=\toks49
\sc@toks@b=\toks50
)
\AttribMinSkip=\skip74
)
(/usr/share/texlive/texmf-dist/tex/latex/was/upgreek.sty
Package: upgreek 2003/02/12 v2.0 (WaS)
Package upgreek Info: Using Euler Roman for upright Greek on input line 31.
\symugrf@m=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `ugrf@m' in version `bold'
(Font)                  U/eur/m/n --> U/eur/b/n on input line 38.
)
(/usr/share/texlive/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2020/01/15 v2.11c `tabularx' package (DPC)
\TX@col@width=\dimen291
\TX@old@table=\dimen292
\TX@old@col=\dimen293
\TX@target=\dimen294
\TX@delta=\dimen295
\TX@cols=\count315
\TX@ftn=\toks51
)
(/usr/share/texlive/texmf-dist/tex/latex/pbox/pbox.sty
Package: pbox 2011/12/07 v1.2 Dynamic parboxes
\pb@xlen=\skip75
)
(/usr/share/texlive/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2021/12/15 v3.1 ragged2e Package
\CenteringLeftskip=\skip76
\RaggedLeftLeftskip=\skip77
\RaggedRightLeftskip=\skip78
\CenteringRightskip=\skip79
\RaggedLeftRightskip=\skip80
\RaggedRightRightskip=\skip81
\CenteringParfillskip=\skip82
\RaggedLeftParfillskip=\skip83
\RaggedRightParfillskip=\skip84
\JustifyingParfillskip=\skip85
\CenteringParindent=\skip86
\RaggedLeftParindent=\skip87
\RaggedRightParindent=\skip88
\JustifyingParindent=\skip89
)
(/usr/share/texlive/texmf-dist/tex/latex/tocloft/tocloft.sty
Package: tocloft 2017/08/31 v2.3i parameterised ToC, etc., typesetting
Package tocloft Info: The document has section divisions on input line 51.
\cftparskip=\skip90
\cftbeforetoctitleskip=\skip91
\cftaftertoctitleskip=\skip92
\cftbeforepartskip=\skip93
\cftpartnumwidth=\skip94
\cftpartindent=\skip95
\cftbeforesecskip=\skip96
\cftsecindent=\skip97
\cftsecnumwidth=\skip98
\cftbeforesubsecskip=\skip99
\cftsubsecindent=\skip100
\cftsubsecnumwidth=\skip101
\cftbeforesubsubsecskip=\skip102
\cftsubsubsecindent=\skip103
\cftsubsubsecnumwidth=\skip104
\cftbeforeparaskip=\skip105
\cftparaindent=\skip106
\cftparanumwidth=\skip107
\cftbeforesubparaskip=\skip108
\cftsubparaindent=\skip109
\cftsubparanumwidth=\skip110
\cftbeforeloftitleskip=\skip111
\cftafterloftitleskip=\skip112
\cftbeforefigskip=\skip113
\cftfigindent=\skip114
\cftfignumwidth=\skip115
\c@lofdepth=\count316
\c@lotdepth=\count317
\cftbeforelottitleskip=\skip116
\cftafterlottitleskip=\skip117
\cftbeforetabskip=\skip118
\cfttabindent=\skip119
\cfttabnumwidth=\skip120
)
(/usr/share/texlive/texmf-dist/tex/latex/marginnote/marginnote.sty
Package: marginnote 2018/08/09 v1.4b non floating margin notes for LaTeX
\c@mn@abspage=\count318
)
(/usr/share/texlive/texmf-dist/tex/latex/marginfix/marginfix.sty
Package: marginfix 2020/05/06 v1.2 Fix Margin Paragraphs
\Mfx@inject@insert=\insert252
\Mfx@marginbox=\box71
\Mfx@marginpos@min=\dimen296
\Mfx@marginpos@max=\dimen297
\Mfx@marginspace=\dimen298
\Mfx@marginheight=\dimen299
\Mfx@piece@content=\box72
\Mfx@piece@count=\count319
\Mfx@mparshift=\dimen300
\marginheightadjustment=\dimen301
\marginposadjustment=\dimen302
\Mfx@strutheight=\dimen303
)
(/usr/share/texlive/texmf-dist/tex/latex/enotez/enotez.sty
(/usr/share/texlive/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2022-01-21 L3 programming layer (loader) 

(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2022-01-12 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count320
\l__pdf_internal_box=\box73
))
(/usr/share/texlive/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2022-01-12 L3 Experimental document command parser
)
(/usr/share/texlive/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2022-01-12 LaTeX2e option processing using LaTeX3 keys
)
(/usr/share/texlive/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty
Package: xtemplate 2022-01-12 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen304
\l__xtemplate_tmp_int=\count321
\l__xtemplate_tmp_muskip=\muskip17
\l__xtemplate_tmp_skip=\skip121
)
Package: enotez 2022/01/04 v0.10d Endnotes for LaTeX2e
\l__enotez_list_preamble_skip=\skip122
\l__enotez_list_postamble_skip=\skip123
\g__enotez_endnote_id_int=\count322
\g__enotez_endnote_mark_int=\count323
\c@endnote=\count324
\g__enotez_list_printed_int=\count325
\l__enotez_tmpa_int=\count326

Package xtemplate Info: Declaring object type 'enotez-list' taking 1
(xtemplate)             argument(s) on line 473.

\l__enotez_list_notes_sep_dim=\dimen305

(/usr/share/texlive/texmf-dist/tex/latex/translations/translations.sty
Package: translations 2022/01/04 v1.11 internationalization of LaTeX2e packages
 (CN)
))
(/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.sty
(/usr/share/texlive/texmf-dist/tex/generic/xstring/xstring.tex
\integerpart=\count327
\decimalpart=\count328
)
Package: xstring 2021/07/21 v1.84 String manipulations (CT)
)
(./Definitions/journalnames.tex)
(/usr/share/texlive/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks52
\thm@bodyfont=\toks53
\thm@headfont=\toks54
\thm@notefont=\toks55
\thm@headpunct=\toks56
\thm@preskip=\skip124
\thm@postskip=\skip125
\thm@headsep=\skip126
\dth@everypar=\toks57
)
\c@theorem=\count329
\c@lemma=\count330
\c@corollary=\count331
\c@proposition=\count332
\c@characterization=\count333
\c@property=\count334
\c@problem=\count335
\c@example=\count336
\c@examplesanddefinitions=\count337
\c@remark=\count338
\c@definition=\count339
\c@hypothesis=\count340
\c@notation=\count341
\c@assumption=\count342
\c@algorithm=\count343

(/usr/share/texlive/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip127
\bibsep=\skip128
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count344
)
(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrextend.sty
Package: scrextend 2021/11/13 v3.35 KOMA-Script package (extend other classes w
ith features of KOMA-Script classes)

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrkbase.sty
Package: scrkbase 2021/11/13 v3.35 KOMA-Script package (KOMA-Script-dependent b
asics and keyval usage)

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrbase.sty
Package: scrbase 2021/11/13 v3.35 KOMA-Script package (KOMA-Script-independent 
basics and keyval usage)

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2021/11/13 v3.35 KOMA-Script package (file load hooks)

(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2021/11/13 v3.35 KOMA-Script package (using LaTeX hooks)


(/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2021/11/13 v3.35 KOMA-Script package (logo)
)))
Applying: [2021/05/01] Usage of raw or classic option list on input line 252.
Already applied: [0000/00/00] Usage of raw or classic option list on input line
 368.
)))
(/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
\MyLen=\skip129
\MyBox=\box74
\leftcolumnlength=\skip130
\extralength=\skip131
\fulllength=\skip132

(/usr/share/texlive/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texlive/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count345
\Gm@cntv=\count346
\c@Gm@tempcnt=\count347
\Gm@bindingoffset=\dimen306
\Gm@wd@mp=\dimen307
\Gm@odd@mp=\dimen308
\Gm@even@mp=\dimen309
\Gm@layoutwidth=\dimen310
\Gm@layoutheight=\dimen311
\Gm@layouthoffset=\dimen312
\Gm@layoutvoffset=\dimen313
\Gm@dimlist=\toks58
)
(/usr/share/texlive/texmf-dist/tex/latex/newfloat/newfloat.sty
Package: newfloat 2019/09/02 v1.1l Defining new floating environments (AR)
)
Package newfloat Info: New float `listing' with options `' on input line 1444.
\c@listing=\count348
Package newfloat Info: float type `listing'=4 on input line 1444.
\c@lolistingdepth=\count349
Package newfloat Info: New float `boxenv' with options `name=Box' on input line
 1445.
\c@boxenv=\count350
Package newfloat Info: float type `boxenv'=8 on input line 1445.
\c@loboxenvdepth=\count351
Package newfloat Info: New float `chart' with options `' on input line 1446.
\c@chart=\count352
Package newfloat Info: float type `chart'=16 on input line 1446.
\c@lochartdepth=\count353
Package newfloat Info: New float `scheme' with options `' on input line 1447.
\c@scheme=\count354
Package newfloat Info: float type `scheme'=32 on input line 1447.
\c@loschemedepth=\count355
Package newfloat Info: New float `figurewide' with options `' on input line 144
8.
\c@figurewide=\count356
Package newfloat Info: float type `figurewide'=64 on input line 1448.
\c@lofigurewidedepth=\count357

(/usr/share/texlive/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2020/10/26 v3.5g Customizing captions (AR)

(/usr/share/texlive/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2020/10/21 v2.2e caption3 kernel (AR)
\captionmargin=\dimen314
\captionmargin@=\dimen315
\captionwidth=\dimen316
\caption@tempdima=\dimen317
\caption@indent=\dimen318
\caption@parindent=\dimen319
\caption@hangindent=\dimen320
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count358
\c@continuedfloat=\count359
Package caption Info: changepage package is loaded.
\caption@adjustwidth@hsize=\dimen321
\caption@adjustwidth@linewidth=\dimen322
Package caption Info: float package is loaded.
)
\@justcentbox=\box75

(/usr/share/texlive/texmf-dist/tex/latex/seqsplit/seqsplit.sty
Package: seqsplit 2006/08/07 v0.1 Splitting long sequences (DNA, RNA, proteins,
 etc.) 
)
\cellWidtha=\skip133
\cellWidthb=\skip134
\cellWidthc=\skip135
\cellWidthd=\skip136
\cellWidthe=\skip137
\cellWidthf=\skip138
\cellWidthg=\skip139
\cellWidthh=\skip140
\cellWidthi=\skip141
\cellWidthj=\skip142
\cellWidthk=\skip143
\cellWidthl=\skip144
\cellWidthm=\skip145
\cellWidthn=\skip146
\cellWidtho=\skip147
\cellWidthp=\skip148
\cellWidthq=\skip149
\cellWidthr=\skip150
\cellWidths=\skip151
\cellWidtht=\skip152
\cellWidthu=\skip153
\cellWidthv=\skip154
\cellWidthw=\skip155
\cellWidthx=\skip156
\cellWidthy=\skip157
\cellWidthz=\skip158
\cellWidthA=\skip159
\cellWidthB=\skip160
\cellWidthC=\skip161
\cellWidthD=\skip162
\cellWidthE=\skip163
\cellWidthF=\skip164
\cellWidthG=\skip165
\cellWidthH=\skip166
\cellWidthI=\skip167
\cellWidthJ=\skip168
\cellWidthK=\skip169
\cellWidthL=\skip170
\cellWidthM=\skip171
\cellWidthN=\skip172
\cellWidthO=\skip173
\cellWidthP=\skip174
\cellWidthQ=\skip175
\cellWidthR=\skip176
\cellWidthS=\skip177
\cellWidthT=\skip178
\cellWidthU=\skip179
\cellWidthV=\skip180
\cellWidthW=\skip181
\cellWidthX=\skip182
\cellWidthY=\skip183
\cellWidthZ=\skip184
)
(/usr/share/texlive/texmf-dist/tex/latex/subfig/subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package
\c@KVtest=\count360
\sf@farskip=\skip185
\sf@captopadj=\dimen323
\sf@capskip=\skip186
\sf@nearskip=\skip187
\c@subfigure=\count361
\c@subfigure@save=\count362
\c@subtable=\count363
\c@subtable@save=\count364
\sf@top=\skip188
\sf@bottom=\skip189
)
Package translations Info: No language package found. I am going to use `englis
h' as default language. on input line 93.

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2021-06-07 v7.00m Hypertext links for LaTeX

(/usr/share/texlive/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/letltxmacro/letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
)
(/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
\@linkdim=\dimen324
\Hy@linkcounter=\count365
\Hy@pagecounter=\count366

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2021-06-07 v7.00m Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hyperref-langpatches.def
File: hyperref-langpatches.def 2021-06-07 v7.00m Hyperref: patches for babel la
nguages
)
(/usr/share/texlive/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
(/usr/share/texlive/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count367

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2021-06-07 v7.00m Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `unicode' set `true' on input line 4073.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 4073.
Package hyperref Info: Option `pdffitwindow' set `true' on input line 4073.
Package hyperref Info: Option `colorlinks' set `true' on input line 4073.
Package hyperref Info: Option `hyperfootnotes' set `true' on input line 4073.
Package hyperref Info: Hyper figures OFF on input line 4192.
Package hyperref Info: Link nesting OFF on input line 4197.
Package hyperref Info: Hyper index ON on input line 4200.
Package hyperref Info: Plain pages OFF on input line 4207.
Package hyperref Info: Backreferencing OFF on input line 4212.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4445.
\c@Hy@tempcnt=\count368
LaTeX Info: Redefining \url on input line 4804.
\XeTeXLinkMargin=\dimen325

(/usr/share/texlive/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/share/texlive/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count369
\Field@Width=\dimen326
\Fld@charsize=\dimen327
Package hyperref Info: Hyper figures OFF on input line 6076.
Package hyperref Info: Link nesting OFF on input line 6081.
Package hyperref Info: Hyper index ON on input line 6084.
Package hyperref Info: backreferencing OFF on input line 6091.
Package hyperref Info: Link coloring ON on input line 6094.
Package hyperref Info: Link coloring with OCG OFF on input line 6101.
Package hyperref Info: PDF/A mode OFF on input line 6106.
LaTeX Info: Redefining \ref on input line 6146.
LaTeX Info: Redefining \pageref on input line 6150.

(/usr/share/texlive/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count370
\c@Item=\count371
\c@Hfootnote=\count372
)
Package hyperref Info: Driver: hpdftex.

(/usr/share/texlive/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2021-06-07 v7.00m Hyperref driver for pdfTeX

(/usr/share/texlive/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count373
\c@bookmark@seq@number=\count374

(/usr/share/texlive/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)

(/usr/share/texlive/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
86.
)
\Hy@SectionHShift=\skip190
)
(/usr/share/texlive/texmf-dist/tex/latex/cleveref/cleveref.sty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: `hyperref' support loaded on input line 2370.
LaTeX Info: Redefining \cref on input line 2370.
LaTeX Info: Redefining \Cref on input line 2370.
LaTeX Info: Redefining \crefrange on input line 2370.
LaTeX Info: Redefining \Crefrange on input line 2370.
LaTeX Info: Redefining \cpageref on input line 2370.
LaTeX Info: Redefining \Cpageref on input line 2370.
LaTeX Info: Redefining \cpagerefrange on input line 2370.
LaTeX Info: Redefining \Cpagerefrange on input line 2370.
LaTeX Info: Redefining \labelcref on input line 2370.
LaTeX Info: Redefining \labelcpageref on input line 2370.
Package cleveref Info: `amsthm' support loaded on input line 3026.
Package cleveref Info: `subfig' support loaded on input line 3164.
Package cleveref Info: always capitalise cross-reference names on input line 78
25.
Package cleveref Info: always capitalise cross-reference names on input line 78
52.
Package cleveref Info: no abbreviation of names on input line 7852.
)
LaTeX Font Info:    Trying to load font information for T1+ppl on input line 93
.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/t1ppl.fd
File: t1ppl.fd 2001/06/04 font definitions for T1/ppl.
) (./template.aux)
\openout1 = `template.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 93.
LaTeX Font Info:    ... okay on input line 93.

(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count375
\scratchdimen=\dimen328
\scratchbox=\box76
\nofMPsegments=\count376
\nofMParguments=\count377
\everyMPshowfont=\toks59
\MPscratchCnt=\count378
\MPscratchDim=\dimen329
\MPnumerator=\count379
\makeMPintoPDFobject=\count380
\everyMPtoPDFconversion=\toks60
)
Package lastpage Info: Please have a look at the pageslts package at
(lastpage)             https://www.ctan.org/pkg/pageslts
(lastpage)             ! on input line 93.
LaTeX Info: Redefining \microtypecontext on input line 93.
Package microtype Info: Applying patch `item' on input line 93.
Package microtype Info: Applying patch `toc' on input line 93.
Package microtype Info: Applying patch `eqnum' on input line 93.
Package microtype Info: Applying patch `footnote' on input line 93.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `alltext-nott'.
LaTeX Info: Redefining \showhyphens on input line 93.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of interword spacing.
Package microtype Info: No adjustment of character kerning.
 (/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-ppl.cfg
File: mt-ppl.cfg 2005/11/16 v1.6 microtype config. file: Palatino (RS)
)
(/usr/share/texlive/texmf-dist/tex/latex/translations/translations-basic-dictio
nary-english.trsl
File: translations-basic-dictionary-english.trsl (english translation file `tra
nslations-basic-dictionary')
)
Package translations Info: loading dictionary `translations-basic-dictionary' f
or `english'. on input line 93.

*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includehead includefoot 
* h-part:(L,W,R)=(167.01753pt, 394.35522pt, 36.13512pt)
* v-part:(T,H,B)=(51.21504pt, 772.49225pt, 21.33955pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=394.35522pt
* \textheight=688.49225pt
* \oddsidemargin=94.74754pt
* \evensidemargin=94.74754pt
* \topmargin=-21.05495pt
* \headheight=12.0pt
* \headsep=24.0pt
* \topskip=10.0pt
* \footskip=48.0pt
* \marginparwidth=116.65646pt
* \marginparsep=14.51074pt
* \columnsep=10.0pt
* \skip\footins=17.07182pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemargintrue
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package newfloat Info: `float' package detected.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring ON on input line 93.
(/usr/share/texlive/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section

(/usr/share/texlive/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count381
)
LaTeX Info: Redefining \ref on input line 93.
LaTeX Info: Redefining \pageref on input line 93.
LaTeX Info: Redefining \nameref on input line 93.

(./template.out) (./template.out)
\@outlinefile=\write4
\openout4 = `template.out'.

LaTeX Font Info:    Trying to load font information for TS1+ppl on input line 9
3.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ts1ppl.fd
File: ts1ppl.fd 2001/06/04 font definitions for TS1/ppl.
)
Underfull \hbox (badness 10000) in paragraph at lines 93--93
[][]\T1/ppl/m/n/7 (+20) creativecommons.org/licenses/by/[][]
 []

LaTeX Font Info:    Trying to load font information for OT1+ppl on input line 9
3.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ppl.fd
File: ot1ppl.fd 2001/06/04 font definitions for OT1/ppl.
)
LaTeX Font Info:    Trying to load font information for OML+zplm on input line 
93.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/omlzplm.fd
File: omlzplm.fd 2002/09/08 Fontinst v1.914 font definitions for OML/zplm.
)
LaTeX Font Info:    Trying to load font information for OMS+zplm on input line 
93.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/omszplm.fd
File: omszplm.fd 2002/09/08 Fontinst v1.914 font definitions for OMS/zplm.
)
LaTeX Font Info:    Trying to load font information for OMX+zplm on input line 
93.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/omxzplm.fd
File: omxzplm.fd 2002/09/08 Fontinst v1.914 font definitions for OMX/zplm.
)
LaTeX Font Info:    Font shape `U/msa/m/n' will be
(Font)              scaled to size 10.42007pt on input line 93.

(/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Font shape `U/msa/m/n' will be
(Font)              scaled to size 7.91925pt on input line 93.
LaTeX Font Info:    Font shape `U/msa/m/n' will be
(Font)              scaled to size 6.25204pt on input line 93.
LaTeX Font Info:    Font shape `U/msb/m/n' will be
(Font)              scaled to size 10.42007pt on input line 93.

(/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)
LaTeX Font Info:    Font shape `U/msb/m/n' will be
(Font)              scaled to size 7.91925pt on input line 93.
LaTeX Font Info:    Font shape `U/msb/m/n' will be
(Font)              scaled to size 6.25204pt on input line 93.
LaTeX Font Info:    Trying to load font information for OT1+zplm on input line 
93.

(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1zplm.fd
File: ot1zplm.fd 2002/09/08 Fontinst v1.914 font definitions for OT1/zplm.
)
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `zplm' (encoding: OT1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.

(/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-eur.cfg
File: mt-eur.cfg 2006/07/31 v1.1 microtype config. file: AMS Euler Roman (RS)
)
LaTeX Font Info:    Redeclaring symbol font `AMSb' on input line 93.
LaTeX Font Info:    Overwriting symbol font `AMSb' in version `normal'
(Font)                  U/msb/m/n --> U/msb/m/n on input line 93.
LaTeX Font Info:    Overwriting symbol font `AMSb' in version `bold'
(Font)                  U/msb/m/n --> U/msb/m/n on input line 93.
Package marginnote Info: Margin note 1.1 is on absolute page 1 on input line 93
.
Package marginnote Info: right page because not two side mode on input line 93.


Underfull \hbox (badness 10000) in paragraph at lines 93--93
[][]\T1/ppl/m/n/7 (+20) creativecommons.org/licenses/by/[][]
 []

Package marginnote Info: xpos seems to be 167.01753pt on input line 93.
Package epstopdf Info: Source file: <Definitions/logo-mdpi.eps>
(epstopdf)                    date: 2025-05-19 14:46:52
(epstopdf)                    size: 563678 bytes
(epstopdf)             Output file: <Definitions/logo-mdpi-eps-converted-to.pdf
>
(epstopdf)                    date: 2025-05-19 15:03:51
(epstopdf)                    size: 9059 bytes
(epstopdf)             Command: <repstopdf --outfile=Definitions/logo-mdpi-eps-
converted-to.pdf Definitions/logo-mdpi.eps>
(epstopdf)             \includegraphics on input line 93.
Package epstopdf Info: Output file is already uptodate.
<Definitions/logo-mdpi-eps-converted-to.pdf, id=94, 28.4523pt x 28.4523pt>
File: Definitions/logo-mdpi-eps-converted-to.pdf Graphic file (type pdf)
<use Definitions/logo-mdpi-eps-converted-to.pdf>
Package pdftex.def Info: Definitions/logo-mdpi-eps-converted-to.pdf  used on in
put line 93.
(pdftex.def)             Requested size: 28.45352pt x 28.45274pt.
<Definitions/logo-orcid.pdf, id=96, 102.3825pt x 102.3825pt>
File: Definitions/logo-orcid.pdf Graphic file (type pdf)
<use Definitions/logo-orcid.pdf>
Package pdftex.def Info: Definitions/logo-orcid.pdf  used on input line 93.
(pdftex.def)             Requested size: 9.10509pt x 9.10466pt.
LaTeX Font Info:    Font shape `U/msa/m/n' will be
(Font)              scaled to size 8.33606pt on input line 93.
LaTeX Font Info:    Font shape `U/msa/m/n' will be
(Font)              scaled to size 5.21004pt on input line 93.
LaTeX Font Info:    Font shape `U/msb/m/n' will be
(Font)              scaled to size 8.33606pt on input line 93.
LaTeX Font Info:    Font shape `U/msb/m/n' will be
(Font)              scaled to size 5.21004pt on input line 93.
LaTeX Font Info:    Trying to load font information for T1+cmss on input line 9
3.
(/usr/share/texlive/texmf-dist/tex/latex/base/t1cmss.fd
File: t1cmss.fd 2019/12/16 v2.5j Standard LaTeX font definitions
)
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `cmss' (encoding: T1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.

Underfull \hbox (badness 10000) in paragraph at lines 102--103

 []


Underfull \hbox (badness 10000) in paragraph at lines 104--106

 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 23.60004pt, for example:
(fancyhdr)                \setlength{\headheight}{23.60004pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-11.60004pt}.

[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map} <./Definitions/logo-mdpi-ep
s-converted-to.pdf> <./Definitions/logo-orcid.pdf>]
Underfull \hbox (badness 10000) in paragraph at lines 107--108

 []


Underfull \hbox (badness 10000) in paragraph at lines 109--111

 []


Underfull \hbox (badness 10000) in paragraph at lines 112--113

 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[2]
<SIU_Lake_Study_Area_Map.jpg, id=220, 462.2871pt x 260.4129pt>
File: SIU_Lake_Study_Area_Map.jpg Graphic file (type jpg)
<use SIU_Lake_Study_Area_Map.jpg>
Package pdftex.def Info: SIU_Lake_Study_Area_Map.jpg  used on input line 132.
(pdftex.def)             Requested size: 384.1122pt x 216.37708pt.

Underfull \hbox (badness 10000) in paragraph at lines 137--138

 []


Package array Warning: Column C is already defined on input line 143.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[3 <./SIU_Lake_Study_Area_Map.jpg>]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[4]

Package array Warning: Column C is already defined on input line 176.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[5]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[6]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[7]
<image2.png, id=338, 167.535pt x 154.7235pt>
File: image2.png Graphic file (type png)
<use image2.png>
Package pdftex.def Info: image2.png  used on input line 288.
(pdftex.def)             Requested size: 189.28882pt x 174.8166pt.
<image1.png, id=340, 173.7765pt x 154.395pt>
File: image1.png Graphic file (type png)
<use image1.png>
Package pdftex.def Info: image1.png  used on input line 292.
(pdftex.def)             Requested size: 189.28882pt x 168.18591pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[8]
<TP.jpg, id=354, 469.755pt x 339.4281pt>
File: TP.jpg Graphic file (type jpg)
<use TP.jpg>
Package pdftex.def Info: TP.jpg  used on input line 308.
(pdftex.def)             Requested size: 126.19655pt x 89.62291pt.
<TSS.jpg, id=356, 469.755pt x 339.4281pt>
File: TSS.jpg Graphic file (type jpg)
<use TSS.jpg>
Package pdftex.def Info: TSS.jpg  used on input line 312.
(pdftex.def)             Requested size: 126.19655pt x 89.62291pt.
<EC.jpg, id=358, 469.755pt x 339.4281pt>
File: EC.jpg Graphic file (type jpg)
<use EC.jpg>
Package pdftex.def Info: EC.jpg  used on input line 316.
(pdftex.def)             Requested size: 126.19655pt x 89.62291pt.

Overfull \hbox (6.73442pt too wide) in paragraph at lines 308--317
 [][][][][][] 
 []


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[9 <./TP.jpg> <./TSS.jpg> <./EC.jpg> <./image2.png> <./image1.png>]
<HAB Suscpetibility.jpg, id=386, 794.97pt x 614.295pt>
File: HAB Suscpetibility.jpg Graphic file (type jpg)
<use HAB Suscpetibility.jpg>
Package pdftex.def Info: HAB Suscpetibility.jpg  used on input line 326.
(pdftex.def)             Requested size: 384.1122pt x 296.81728pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[10 <./HAB Suscpetibility.jpg>]
<image3.png, id=397, 142.569pt x 96.9075pt>
File: image3.png Graphic file (type png)
<use image3.png>
Package pdftex.def Info: image3.png  used on input line 341.
(pdftex.def)             Requested size: 118.30777pt x 80.41988pt.
<image4.png, id=398, 145.5255pt x 95.5935pt>
File: image4.png Graphic file (type png)
<use image4.png>
Package pdftex.def Info: image4.png  used on input line 345.
(pdftex.def)             Requested size: 118.30777pt x 77.71474pt.
<image5.png, id=399, 145.5255pt x 95.5935pt>
File: image5.png Graphic file (type png)
<use image5.png>
Package pdftex.def Info: image5.png  used on input line 349.
(pdftex.def)             Requested size: 118.30777pt x 77.71474pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[11 <./image3.png> <./image4.png> <./image5.png>]
<SIU_19feb2022.jpg, id=416, 267.1581pt x 216.81pt>
File: SIU_19feb2022.jpg Graphic file (type jpg)
<use SIU_19feb2022.jpg>
Package pdftex.def Info: SIU_19feb2022.jpg  used on input line 373.
(pdftex.def)             Requested size: 177.45863pt x 144.01472pt.
<SHAP_Impact_BarPlot_19th_Feb.png, id=417, 3011.25pt x 2409.0pt>
File: SHAP_Impact_BarPlot_19th_Feb.png Graphic file (type png)
<use SHAP_Impact_BarPlot_19th_Feb.png>
Package pdftex.def Info: SHAP_Impact_BarPlot_19th_Feb.png  used on input line 3
77.
(pdftex.def)             Requested size: 177.45863pt x 141.96068pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[12 <./SIU_19feb2022.jpg> <./SHAP_Impact_BarPlot_19th_Feb.png>]
<ndci_time_series_with_news.jpg, id=435, 867.24pt x 433.62pt>
File: ndci_time_series_with_news.jpg Graphic file (type jpg)
<use ndci_time_series_with_news.jpg>
Package pdftex.def Info: ndci_time_series_with_news.jpg  used on input line 398
.
(pdftex.def)             Requested size: 384.1122pt x 192.05714pt.


Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[13 <./ndci_time_series_with_news.jpg>]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[14]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[15]
Underfull \hbox (badness 10000) in paragraph at lines 519--519

 []

(./template.bbl

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[16]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[17]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[18]

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[19]) 
AED: lastpage setting LastPage

Package fancyhdr Warning: \headheight is too small (12.0pt): 
(fancyhdr)                Make it at least 16.18796pt, for example:
(fancyhdr)                \setlength{\headheight}{16.18796pt}.
(fancyhdr)                You might also make \topmargin smaller to compensate:

(fancyhdr)                \addtolength{\topmargin}{-4.18796pt}.

[20] (./template.aux)
Package rerunfilecheck Info: File `template.out' has not changed.
(rerunfilecheck)             Checksum: 17C40DEF4E9FFE738A6961D8C9F5390A;4010.
 ) 
Here is how much of TeX's memory you used:
 34237 strings out of 480171
 643326 string characters out of 5894490
 995227 words of memory out of 5000000
 50920 multiletter control sequences out of 15000+600000
 541330 words of font info for 440 fonts, out of 8000000 for 9000
 59 hyphenation exceptions out of 8191
 123i,12n,120p,2044b,803s stack positions out of 5000i,500n,10000p,200000b,80000s
 </home/<USER>/.texlive2021/texmf-var/fonts/pk/ljfour/jknappen/ec/ecss0500.60
0pk>{/usr/share/texlive/texmf-dist/fonts/enc/dvips/base/8r.enc}</usr/share/texl
ive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texlive/tex
mf-dist/fonts/type1/public/mathpazo/fplmri.pfb></usr/share/texlive/texmf-dist/f
onts/type1/urw/palatino/uplb8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/u
rw/palatino/uplr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/palatino/
uplri8a.pfb>
Output written on template.pdf (20 pages, 5758167 bytes).
PDF statistics:
 589 PDF objects out of 1000 (max. 8388607)
 498 compressed objects within 5 object streams
 158 named destinations out of 1000 (max. 500000)
 103690 words of extra memory for PDF output out of 106986 (max. 10000000)

