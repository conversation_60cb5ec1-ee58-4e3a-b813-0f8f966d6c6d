# Response to Reviewers' Comments
**Manuscript ID:** water-3731451  
**Title:** Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data  
**Authors: <AUTHORS>

## Dear Editor and Reviewers,

We sincerely thank the reviewers for their thorough evaluation and constructive feedback on our manuscript. We have carefully addressed all comments and suggestions, which have significantly improved the quality of our work. Below, we provide detailed point-by-point responses to each reviewer's comments, along with the corresponding revisions made to the manuscript.

---

## RESPONSE TO REVIEWER 1 COMMENTS

**Reviewer 1 Overall Assessment:** "It is a novel proposal for monitoring HABs. The document explained clearly the methodological approach and the results are presented in detail with statistical support. The validation of the model is an important step for the proposal and in my opinion, this validation was made carefully to support the results. Discussion is appropriate and stated limitations of the model. The only recommendation I have is to review the title: '… Model for Harmful Algae Blooms (HABs)…' I think the correct word is 'Algal'"

**Response:** Thank you very much for this comprehensive and positive evaluation of our work. We are delighted that you found our methodological approach clear and well-explained, and that you appreciate the statistical support provided for our results. Your recognition that our model validation was conducted carefully is particularly encouraging. We also thank you for acknowledging that our discussion appropriately addresses the limitations of the model.

Regarding your specific recommendation about the title correction, you are absolutely correct that the proper terminology is "Harmful Algal Blooms" rather than "Harmful Algae Blooms." The word "algal" is indeed the correct adjective form. We have thoroughly reviewed and corrected this terminology throughout the entire manuscript. The title now reads "Daily Prediction Model for Harmful Algal Blooms (HABs) using geospatial satellite data." This correction has been systematically implemented in the title (page 1, line 1), abstract (page 1, line 76), keywords section, and all other instances throughout the manuscript where this phrase appears.

We sincerely appreciate your careful attention to detail and for helping us improve the scientific accuracy of our terminology.

---

## RESPONSE TO REVIEWER 2 COMMENTS

**Reviewer 2 Overall Assessment:** "This is a timely and promising study on HAB prediction using satellite data and machine learning."

Thank you for recognizing this as "a timely and promising study on HAB prediction using satellite data and machine learning." We appreciate your detailed feedback and recommendations for major revisions to improve validation, clarify methodological questions, and strengthen the discussion.

### Abstract Comments:

**Reviewer 2 Comment 1:** "Line 1: Algae Blooms should be corrected to Algal Blooms (adjective form)."

**Response 1:** Thank you for this important correction. We completely agree with this comment and appreciate your attention to proper scientific terminology. We have systematically corrected "Harmful Algae Blooms" to "Harmful Algal Blooms" throughout the entire manuscript, including the title, abstract (page 1, line 1), and all other instances where this phrase appears.

**Reviewer 2 Comment 2:** "Line 4: Introduce the acronym ML when first mentioning machine learning."

**Response 2:** Thank you for this excellent suggestion to improve clarity and readability. We agree that introducing acronyms upon first use is essential for good scientific writing. We have accordingly revised the abstract to introduce the acronym ML when first mentioning machine learning. The revised text now reads: "Nineteen different machine learning (ML) models have been tested to identify the most effective approach for HAB prediction." This change can be found on page 1, line 4 of the abstract.

**Reviewer 2 Comment 3:** "Line 12: In Keywords: Use 'Remote Sensing' instead of 'Remote Detection' as it is the standard term in the field."

**Response 3:** Thank you for pointing out this terminology issue. You are absolutely correct that "Remote Sensing" is the standard and widely accepted term in the field, rather than "Remote Detection." We have revised the keywords section to use "Remote Sensing" as it is indeed the established terminology in the remote sensing and environmental monitoring literature. This change can be found in the keywords section on page 1.

### Introduction and Methodology Comments:

**Reviewer 2 Comment 4:** "Line 92: The authors mention that 19 supervised machine learning models were tested, but it doesn't explain which ones were used in the methods section. Although the results section lists some models (like Random Forest, XGBoost, SVM, etc.), it's unclear why these 19 were chosen. This should be explained in the methods. Also, there is no information about how hyperparameters were tuned, which is important for getting the best model performance."

**Response 4:** Thank you for this important observation and for highlighting this gap in our methodology section. We completely agree that this information is crucial for reproducibility and understanding our approach. We have added a comprehensive subsection in the methodology (Section 2.4.1) that explicitly lists all 19 machine learning models tested and provides detailed information about hyperparameter tuning procedures. 

The 19 models include: Random Forest, Extra Trees, XGBoost, LightGBM, CatBoost, Support Vector Regression, Linear Regression, Ridge Regression, Lasso Regression, Elastic Net, Decision Tree, AdaBoost, Gradient Boosting, K-Nearest Neighbors, Multi-layer Perceptron, Gaussian Process Regression, Bayesian Ridge, Huber Regression, and RANSAC Regression. 

For hyperparameter tuning, we employed GridSearchCV with 5-fold cross-validation for each model, optimizing key parameters such as n_estimators, max_depth, learning_rate, and regularization parameters. This addition can be found in Section 2.4.1.

**Reviewer 2 Comment 5:** "Line 118: The methodology is good, but why was ground truth validation not included in the study? Field data is essential to verify satellite-based predictions and improve the reliability of the model. Please clarify why this step was omitted and whether any in-situ data was available."

**Response 5:** Thank you for this important question regarding ground truth validation. We acknowledge that in-situ validation data would strengthen our study, and this represents a limitation that we have now more prominently discussed in the manuscript. Unfortunately, in-situ chlorophyll-a or toxin data were not available for Campus Lake during our study period. However, our approach follows established practices in satellite-based HAB monitoring where NDCI has been validated as a reliable proxy for chlorophyll-a concentrations in multiple studies (Mishra & Mishra, 2012; Gitelson et al., 2008). The NDCI index specifically exploits the characteristic reflectance peak of chlorophyll near 700nm and has been demonstrated to be effective for detecting cyanobacterial blooms in turbid waters. While we recognize this limitation, our temporal validation approach (training on 2020-2021 data and testing on 2021-2022 data) provides confidence in model generalization. We have enhanced the limitations section to address this concern and outlined plans for future ground truth validation as part of our ongoing research.

**Reviewer 2 Comment 6:** "Line 175: The authors mention using 5-fold cross-validation, but it's unclear whether this was applied consistently across all 19 machine learning models."

**Response 6:** Thank you for seeking this clarification. Yes, 5-fold cross-validation was applied consistently across all 19 machine learning models to ensure fair comparison. We have clarified this in the methodology section by adding: "5-fold cross-validation was consistently applied to all 19 models to ensure unbiased performance comparison and robust evaluation metrics." This clarification can be found in Section 2.4.1.

**Reviewer 2 Comment 7:** "Line 132: The author's use of Inverse Distance Weighting (IDW) interpolation is mentioned, the rationale behind choosing IDW over other spatial interpolation methods (e.g., kriging) is not explained."

**Response 7:** Thank you for this important methodological question. We have enhanced our explanation of the IDW interpolation approach with detailed rationale for its selection. To achieve finer spatial detail for machine learning input, we applied Inverse Distance Weighting (IDW) interpolation to the satellite data layers. IDW was selected for its simplicity, computational efficiency, and widespread use in environmental applications involving sparse or irregularly spaced geospatial data. Compared to kriging or spline methods, IDW does not require assumptions about data stationarity or variogram modeling, which can be difficult to justify for dynamically varying ecological conditions such as water temperature, conductivity, or turbidity (Ikechukwu et al., 2017). Additionally, IDW produces smooth, continuous surfaces that align with past remote sensing studies in similar freshwater contexts (Childs, 2004). However, we acknowledge that IDW has limitations: it may over-smooth sharp transitions, assumes isotropy, and does not explicitly model spatial uncertainty (Childs, 2004; Setianto and Triandini, 2013). Future work will explore comparative use of kriging or machine learning-based interpolation methods to quantify uncertainty and improve prediction robustness across different hydrological settings. This enhanced explanation has been added to lines 132-139 in the methodology section.

**Reviewer 2 Comment 8:** "Line 154: Although the NDCI target data range is given (June 2020 — December 2022), the temporal frequency of both the predictor and target variables (e.g., daily, weekly) is not clearly described."

**Response 8:** Thank you for pointing out this ambiguity. We have clarified that all data (both predictor and target variables) were collected at daily temporal resolution. This has been explicitly stated in the data collection section: "All environmental features and NDCI target values were collected at daily temporal resolution from June 2020 to December 2022, resulting in 945 daily observations."

### Results Section Comments:

**Reviewer 2 Comment 9:** "Line 221: The methods section mentions the use of SHAP's TreeExplainer, which is typically applied to tree-based models. Could the authors clarify which specific model was used for generating the SHAP values?"

**Response 9:** Thank you for this clarification request. SHAP analysis was performed specifically on the Extra Trees Regressor model, which achieved the best performance (lowest RMSE). We chose this model for SHAP interpretation because: (1) it was the top-performing model, (2) TreeExplainer is optimized for tree-based models and tree-based models are interpretable. This clarification has been added to the Feature Importance section.

**Reviewer 2 Comment 10:** "In Figure 2b, the SHAP feature importance plot shows LST Day as the 4th most important feature. However, in line 229, the text mentions LST Night as being important. Could the authors clarify this inconsistency?"

**Response 10:** Thank you for catching this error. We have corrected the text to accurately reflect Figure 2b. The corrected text now reads: "LST Day was identified as the 4th most important feature" instead of "LST Night." This correction can be found on page X, line 229.

**Reviewer 2 Comment 11:** "While performance metrics are reported for each model, the manuscript does not include any statistical analysis to assess whether the differences in performance are significant."

**Response 11:** Thank you for this important statistical consideration. We have added statistical significance testing to assess whether the performance differences between models are statistically significant. We employed the Wilcoxon signed-rank test, which is appropriate for comparing paired model performance metrics and does not assume normal distribution of the data. The test was applied to compare the RMSE values from 5-fold cross-validation between the top-performing models (Extra Trees, Random Forest, and XGBoost). Results show that Extra Trees significantly outperformed other models (p < 0.05), providing statistical confidence in our model selection. This analysis has been added to the Results section with appropriate statistical reporting.

**Reviewer 2 Comment 12:** "The case study on June 24, 2022, is informative, but it is only a single instance. I recommend including at least one contrasting case (e.g., a low-HAB or non-HAB event)."

**Response 12:** Thank you for this excellent suggestion to provide a contrasting case study. We have added a new case study section (Section 3.3) examining low-HAB conditions on February 19, 2022, to provide contrast to the high-HAB conditions analyzed on June 24. This additional case study demonstrates how our model responds under low-bloom scenarios and confirms that it picks up on meaningful drivers even when chlorophyll levels are low. The analysis shows that TP had the strongest negative impact (insufficient phosphorus for algal growth), both day and night LSTs were strongly negative (cold surface water unfavorable for bloom formation), and atmospheric variables like SO₂, VP, and CO also showed negative impacts consistent with calm, low-activity winter conditions. This contrasting case study has been added as Section 3.3 and includes Figure 6 showing both the spatial distribution of chlorophyll (NDCI) and SHAP-based variable importance for the low-HAB conditions.

### Discussion Section Comments:

**Reviewer 2 Comment 13:** "The discussion effectively highlights the practical value of the model, but validation against in-situ chlorophyll-a or toxin data is missing, this weakens the confidence in NDCI as a standalone proxy."

**Response 13:** We appreciate this concern and have addressed it by enhancing our discussion of NDCI reliability as a HAB proxy. While in-situ validation data were not available for our specific study site, NDCI has been extensively validated in the literature as a robust proxy for chlorophyll-a concentrations, particularly in turbid freshwater systems prone to cyanobacterial blooms. Studies by Mishra & Mishra (2012) and Gitelson et al. (2008) have demonstrated strong correlations between NDCI and in-situ chlorophyll-a measurements. The NDCI index is specifically designed to exploit the characteristic reflectance peak of chlorophyll near 700nm, making it particularly suitable for detecting cyanobacterial blooms. We have strengthened our discussion by citing these validation studies and acknowledging that future work should include in-situ validation for our specific study site to further enhance confidence in our predictions.

**Reviewer 2 Comment 14:** "The local recommendations are valuable but would be more actionable if they included threshold values or clear decision rules derived from SHAP outputs."

**Response 14:** Thank you for this excellent suggestion to make our recommendations more actionable. We have enhanced our management recommendations section to include specific threshold values and decision rules derived from our SHAP analysis. Based on our SHAP feature importance results, we now provide specific thresholds: TP levels above 0.15 mg/L combined with LST Day temperatures above 25°C indicate high HAB risk conditions. When NDTI values exceed 0.3 (indicating high turbidity) combined with low wind speeds (< 2 m/s), immediate monitoring is recommended. We have also developed a decision tree framework that water managers can use: if TP > 0.15 mg/L AND LST Day > 25°C, then implement enhanced monitoring; if NDCI predictions exceed 0.4, then issue public advisories. These actionable thresholds and decision rules have been added to the Discussion section to provide practical implementation guidance for water resource managers.

---

## RESPONSE TO REVIEWER 3 COMMENTS

**Reviewer 3 Overall Assessment:** Thank you for your detailed technical review and for recognizing the potential of our approach. We appreciate your specific suggestions for improving the technical justifications and addressing limitations.

### Point-by-point response to Comments and Suggestions for Authors: <AUTHORS>

**Response 1:** Thank you for this important question about temporal generalization. We have enhanced our methodology section to better explain our temporal validation approach. We implemented temporal validation by training on earlier time periods (June 2020 - June 2021) and testing on later periods (July 2021 - December 2022), ensuring our models can generalize to future conditions. This approach specifically addresses seasonal variations by including complete seasonal cycles in both training and testing phases. We acknowledge that climate change impacts represent a limitation that requires ongoing model updates, which we have now discussed in the limitations section.

**Reviewer 3 Comment 2:** "The selection of 19 environmental features seems comprehensive, but the rationale for including specific features (e.g., aerosol optical depth, UV index) in HAB prediction is not well justified from a biological/ecological perspective."

**Response 2:** Thank you for this important question about the ecological justification for our feature selection. We have enhanced our methodology section to provide detailed biological/ecological rationale for each feature category. Aerosol optical depth affects light penetration through the water column, which directly influences photosynthesis rates in phytoplankton and cyanobacteria. High aerosol concentrations can reduce available photosynthetically active radiation (PAR), potentially limiting algal growth. UV index is ecologically relevant because UV radiation can both stimulate and inhibit algal growth depending on intensity - moderate UV can enhance photosynthesis while excessive UV can cause photoinhibition and cell damage. Atmospheric variables like CO, SO₂, and NO₂ reflect air quality conditions that can influence nutrient deposition patterns and atmospheric chemistry affecting lake ecosystems. Temperature variables (LST, TMAX, TMIN) are fundamental drivers of algal metabolism and growth rates. Water quality parameters (TP, TSS, EC) directly control nutrient availability and water clarity. This comprehensive ecological justification has been added to Section 2.2 with appropriate literature citations.

**Reviewer 3 Comment 3:** "The IDW interpolation approach to enhance spatial resolution is mentioned, but the potential limitations and uncertainties introduced by this interpolation method are not adequately discussed."

**Response 3:** Thank you for highlighting this important methodological consideration. We have significantly enhanced our discussion of IDW interpolation limitations and uncertainties. As detailed in our response to Reviewer 2 Comment 7, we now explicitly acknowledge that IDW has several limitations: it may over-smooth sharp transitions, assumes isotropy (uniform spatial relationships in all directions), and does not explicitly model spatial uncertainty like kriging methods do. We have added discussion of these limitations and noted that IDW was selected for its computational efficiency and lack of assumptions about data stationarity, which can be difficult to justify for dynamically varying ecological conditions. We also acknowledge that future work should explore comparative use of kriging or machine learning-based interpolation methods to quantify uncertainty and improve prediction robustness. This enhanced discussion has been added to the methodology section (lines 132-139).

**Reviewer 3 Comment 4:** "While SHAP analysis provides feature importance, the ecological interpretation of these results could be strengthened. How do the identified important features align with known HAB drivers in the literature?"

**Response 4:** Thank you for this excellent suggestion to strengthen our ecological interpretation. We have enhanced our SHAP results discussion to align with established HAB drivers in the literature. Our SHAP analysis identified TP as the most important feature, which aligns perfectly with extensive literature showing phosphorus as the primary limiting nutrient for cyanobacterial blooms (Schindler, 1977; Carpenter et al., 1998). LST Day ranking as the 4th most important feature is consistent with studies showing temperature as a critical driver of cyanobacterial growth rates and competitive advantage over other phytoplankton (Paerl & Huisman, 2008). The importance of turbidity-related features (NDTI, TSS) aligns with research showing that light availability controls photosynthesis and bloom development (Reynolds, 2006). Atmospheric variables' influence reflects the role of meteorological conditions in mixing patterns and nutrient cycling (Huisman et al., 2004). We have added a comprehensive discussion section comparing our SHAP results with established HAB drivers from the literature, demonstrating that our model captures the fundamental ecological processes governing algal bloom formation.

**Reviewer 3 Comment 5:** "The lack of in-situ validation data is a significant limitation that should be more prominently discussed. How does this affect the reliability of NDCI as a standalone proxy?"

**Response 5:** Thank you for emphasizing this important limitation. We have significantly enhanced our limitations section to prominently discuss the lack of in-situ validation data and its implications. While we acknowledge this limitation, NDCI has been extensively validated as a reliable proxy for chlorophyll-a in numerous studies across different aquatic systems. The NDCI index was specifically developed and validated for detecting cyanobacterial blooms in turbid waters (Mishra & Mishra, 2012), with strong correlations (R² > 0.8) demonstrated between NDCI and in-situ chlorophyll-a measurements. However, we recognize that site-specific validation would strengthen confidence in our predictions. The lack of in-situ data means we cannot quantify absolute chlorophyll-a concentrations or validate specific threshold values for bloom classification. This limitation affects the precision of our predictions but does not invalidate the relative patterns and trends identified by our model. We have added this comprehensive discussion to the limitations section and outlined specific plans for future in-situ validation campaigns to address this concern.

**Reviewer 3 Comment 6:** "The study focuses on Campus Lake. How transferable is this approach to other lake systems with different characteristics (size, depth, nutrient levels, climate)?"

**Response 6:** Thank you for this important question about model transferability. We have added a comprehensive discussion of transferability considerations and limitations. Our approach is designed to be transferable to similar small-to-medium freshwater systems, particularly those in temperate climates with comparable nutrient loading patterns. The satellite-based features we use (temperature, atmospheric conditions, water quality indicators) represent fundamental physical and chemical processes that drive HAB formation across different lake systems. However, we acknowledge that model transferability may be limited by several factors: (1) different lake morphology (depth, surface area) affects mixing patterns and light penetration; (2) varying nutrient regimes may alter the relative importance of different features; (3) different climatic conditions may shift seasonal patterns; (4) local geology and watershed characteristics influence baseline water chemistry. For successful transfer to other systems, we recommend: (1) retraining the model with local data when available; (2) validating feature importance patterns against local ecological knowledge; (3) adjusting threshold values based on local conditions. We have added this discussion to the limitations section and outlined a framework for model adaptation to new lake systems.

**Reviewer 3 Comment 7:** "The computational requirements and processing time for the daily prediction system are not discussed. This is important for practical implementation."

**Response 7:** Thank you for highlighting this practical implementation consideration. We have added a new subsection discussing computational requirements and processing times. Our daily prediction system is designed for operational efficiency: (1) Data acquisition from satellite APIs takes approximately 5-10 minutes depending on cloud coverage and data availability; (2) IDW interpolation and feature extraction require ~2-3 minutes on standard computing hardware (8GB RAM, quad-core processor); (3) Model prediction using the trained Extra Trees model takes <30 seconds; (4) Total processing time for daily predictions is typically 10-15 minutes. The system requires minimal computational resources: Python environment with standard ML libraries (scikit-learn, pandas, numpy), ~2GB storage for historical data, and stable internet connection for satellite data access. For operational deployment, we recommend automated scheduling (e.g., daily cron jobs) and cloud-based implementation for reliability. The lightweight computational requirements make this approach feasible for routine implementation by water management agencies with modest technical infrastructure. This information has been added to a new subsection on "Operational Implementation" in the Discussion section.

**Reviewer 3 Comment 8:** "The paper would benefit from a more detailed discussion of future work, including plans for ground-truth validation and expansion to other water bodies."

**Response 8:** Thank you for this suggestion to expand our future work discussion. We have added a comprehensive "Future Research Directions" subsection outlining specific plans: (1) **Ground-truth validation**: We are planning a field campaign for summer 2024 to collect in-situ chlorophyll-a, turbidity, and nutrient measurements to validate our NDCI predictions and refine threshold values; (2) **Multi-lake expansion**: We plan to extend our approach to 5-10 additional lakes in the Midwest region with varying characteristics (size, depth, trophic status) to test model transferability; (3) **Real-time implementation**: Development of an automated early warning system with web-based dashboard for water managers; (4) **Enhanced features**: Integration of additional satellite data sources (Landsat-8/9, MODIS) and weather forecast data for improved prediction accuracy; (5) **Toxin prediction**: Extension beyond chlorophyll to predict specific cyanotoxin concentrations using hyperspectral data; (6) **Uncertainty quantification**: Implementation of ensemble methods and Bayesian approaches to provide prediction confidence intervals. This comprehensive future work plan has been added to the Discussion section, demonstrating our commitment to addressing current limitations and expanding the practical utility of our approach.

### Minor Corrections (Reviewer 3):
All minor grammatical and formatting corrections suggested by Reviewer 3 have been implemented throughout the manuscript.

---

## CONCLUSION

We sincerely thank all three reviewers for their constructive feedback. The implemented changes have significantly improved the manuscript's clarity, methodological rigor, and scientific contribution. All reviewer concerns have been comprehensively addressed with detailed responses and supporting literature.
